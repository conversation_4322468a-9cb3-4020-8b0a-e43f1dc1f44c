import { FormControl, MenuItem, Select, SelectChangeEvent } from "@mui/material";
import Image from "next/image";
import React from "react";

// Use the imported type instead of defining it again
interface SelectEnvItem {
  value: string;
  label: string;
  icon?: string;
}

interface SelectEnvProps {
  heading: string;
  value: string;
  onchange: (event: SelectChangeEvent<string>) => void;
  data: SelectEnvItem[];
  id?: string;
}

const SelectEnv: React.FC<SelectEnvProps> = ({ 
  heading, 
  value, 
  onchange, 
  data,
  id 
}) => {
  const CustomArrowIcon = () => (
    <svg
      width="11"
      height="6"
      viewBox="0 0 11 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.2934 0.275093C10.1176 0.0995297 9.87935 0.000917435 9.63091 0.000917435C9.38247 0.000917435 9.14419 0.0995297 8.96841 0.275093L5.56841 3.67509L2.16841 0.275093C1.99069 0.109493 1.75563 0.0193381 1.51276 0.0236235C1.26988 0.0279083 1.03815 0.126299 0.866384 0.298066C0.694618 0.469832 0.596228 0.701563 0.591943 0.94444C0.587658 1.18732 0.677811 1.42237 0.843411 1.60009L4.90591 5.66259C5.08169 5.83816 5.31997 5.93677 5.56841 5.93677C5.81685 5.93677 6.05513 5.83816 6.23091 5.66259L10.2934 1.60009C10.469 1.42431 10.5676 1.18603 10.5676 0.937593C10.5676 0.689154 10.469 0.450874 10.2934 0.275093Z"
        fill="white"
      />
    </svg>
  );

  return (
    <div className="flex flex-col gap-2">
      <h4 className=" text-gray-200 text-sm font-normal font-inter leading-tight tracking-tight mb-0">
        {heading}
      </h4>
      <FormControl
        sx={{
          m: 1,
          minWidth: 120,
          display: "flex",
          alignSelf: "stretch",
          margin: 0,
        }}
        size="small"
      >
        <Select
          value={value}
          onChange={onchange}
          inputProps={{ "aria-label": "Without label" }}
          IconComponent={CustomArrowIcon}
          data-testid={id}
          sx={{
            display: "flex",
            padding: "8px",
            fontSize: "14px",
            backgroundColor: "#1818184D",
            color: "white",
            justifyContent: "flex-start",
            alignItems: "center",
            gap: "8px",
            alignSelf: "stretch",
            "& .MuiSelect-select": {
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-start",
              padding: "8px",
            },
            "& .MuiOutlinedInput-notchedOutline": {
              border: "none",
            },
            "&:hover .MuiOutlinedInput-notchedOutline": {
              border: "none",
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              border: "none",
            },
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                backgroundColor: "#2a2a2a",
                "& .MuiMenuItem-root": {
                  color: "white",
                  "&:hover": {
                    backgroundColor: "#404040",
                  },
                  "&.Mui-selected": {
                    backgroundColor: "#505050",
                    "&:hover": {
                      backgroundColor: "#606060",
                    },
                  },
                },
              },
            },
          }}
        >
          {data.map((item) => (
            <MenuItem key={item.value} value={item.value}>
              {item.icon ? (
                <div className="flex gap-2 items-center">
                  <span>
                    <Image src={item.icon} alt="icon" className="w-4 h-4" />
                  </span>
                  {item.label}
                </div>
              ) : (
                item.label
              )}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </div>
  );
};

export default SelectEnv;