// store/cutStore.ts
import { create } from "zustand";

export interface CutTime {
  input: string;
  hours: number;
  minutes: number;
  seconds: number;
  frames: number;
  fps: number;
}

interface CutState {
  cutTimes: CutTime[];
  addCutTime: (cut: CutTime) => void;
  setCutTimes: (cuts: CutTime[]) => void;
  clearCutTimes: () => void;
  removeCutTime: (index: number) => void; // optional
}

export const useCutStore = create<CutState>((set, get) => ({
  cutTimes: [],
  addCutTime: (cut) => set((state) => {
    // Remove any existing cut with the same timestamp
    const filteredCuts = state.cutTimes.filter(existing => existing.input !== cut.input);
    const newCutTimes = [...filteredCuts, cut];
    
    // Only log and update if there's actually a change
    if (JSON.stringify(newCutTimes) !== JSON.stringify(state.cutTimes)) {
      return { cutTimes: newCutTimes };
    }
    return state;
  }),
  setCutTimes: (cuts) => set((state) => {
    // Only update if there's actually a change
    if (JSON.stringify(cuts) !== JSON.stringify(state.cutTimes)) {
      return { cutTimes: cuts };
    }
    return state;
  }),
  clearCutTimes: () => set({ cutTimes: [] }),
  removeCutTime: (index) =>
    set((state) => ({
      cutTimes: state.cutTimes.filter((_, i) => i !== index),
    })),
}));
