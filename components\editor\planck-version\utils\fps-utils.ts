import { Overlay, OverlayType, ClipOverlay } from "../types";
import { FPS } from "../constants";

// Global XML FPS override for dynamic project FPS
let xmlFpsOverride: number | null = null;

/**
 * Set XML FPS override for dynamic timeline FPS
 */
export const setXmlFpsOverride = (fps: number | null): void => {
  xmlFpsOverride = fps;  
};

/**
 * Get current XML FPS override
 */
export const getXmlFpsOverride = (): number | null => {
  return xmlFpsOverride;
};

/**
 * Gets the effective FPS for timeline operations based on video overlays
 * Uses the highest FPS from all video overlays, falls back to default FPS
 */
export const getEffectiveFPS = (overlays: Overlay[]): number => {
   
  // If XML FPS override is set, use that (for dynamic project FPS)
  if (xmlFpsOverride !== null) {    
    return xmlFpsOverride;
  }

  // Find video overlays with FPS property
  const videoOverlays = overlays.filter(
    (overlay) => overlay.type === OverlayType.VIDEO && 'fps' in overlay
  ) as ClipOverlay[];

  if (videoOverlays.length === 0) {    
    return FPS; 
  }

  // Get the first video overlay
  const firstVideo = videoOverlays[0];
    
  // Check if the video file name contains fps information (like "60fps" in the filename)
  if (firstVideo.content && typeof firstVideo.content === 'string') {
    const fpsMatch = firstVideo.content.match(/(\d+)fps/i);
    if (fpsMatch && fpsMatch[1]) {
      const fpsFromFilename = parseFloat(fpsMatch[1]);
      if (!isNaN(fpsFromFilename)) {        
        return fpsFromFilename;
      }
    }
  }

  // Fall back to the overlay's fps property or default FPS
  let finalFPS = firstVideo.fps ?? FPS;
  
  // Common FPS correction for misdetected frame rates
  // Many video files are incorrectly detected as 25fps when they're actually 29.97fps
  if (finalFPS === 25) {
    // For testing purposes, assume 25fps should be 29.97fps (NTSC standard)
    finalFPS = 29.97;    
  }  
  return finalFPS;
};

/**
 * Gets the FPS for a specific video overlay
 */
export const getOverlayFPS = (overlay: Overlay): number => {
  if (overlay.type === OverlayType.VIDEO) {
    const clipOverlay = overlay as ClipOverlay;
    return clipOverlay.fps ?? FPS;
  }
  return FPS;
};

/**
 * Converts time to frames using the effective FPS
 */
export const timeToFrames = (timeInSeconds: number, fps: number = FPS): number => {
  return Math.round(timeInSeconds * fps);
};

/**
 * Converts frames to time using the effective FPS
 */
export const framesToTime = (frames: number, fps: number = FPS): number => {
  return frames / fps;
};

/**
 * Formats time with frame precision using the given FPS
 */
export const formatTimeWithFPS = (frames: number, fps: number = FPS): string => {
  // Use standard frame rates for display
  let displayFPS = 30;
  if (fps <= 25) displayFPS = 25;
  else if (fps <= 30) displayFPS = 30;
  else if (fps <= 60) displayFPS = 60;
  
  // Calculate time using actual FPS, but display frames using standard FPS
  const totalSeconds = Math.floor(frames / fps);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  // Convert current position to display frame within the current second
  const currentSecondStart = totalSeconds * fps;
  const frameInSecond = frames - currentSecondStart;
  const displayFrame = Math.floor((frameInSecond / fps) * displayFPS);

  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${displayFrame
      .toString()
      .padStart(2, "0")}`;
  }

  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}.${displayFrame.toString().padStart(2, "0")}`;
};