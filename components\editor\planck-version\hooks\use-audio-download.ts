import { useState, useCallback } from 'react';
import path from 'path';
import { Overlay, OverlayType, SoundOverlay } from '../types';
import { getEffectiveFPS } from '../utils/fps-utils';

// Types
interface DownloadMessage {
  type: "success" | "error" | null;
  message: string;
}

interface AudioProcessingStrategy {
  shouldUse: (overlaysToDownload: SoundOverlay[]) => boolean;
  process: (overlaysToDownload: SoundOverlay[], audioContext: AudioContext, currentFPS: number) => Promise<number>;
}

interface UseAudioDownloadProps {
  overlays: Overlay[];
  selectedSegment: any | null;
  audioType: string;
  durationInFrames?: number;
  projectUrl?: string;
  pathRef?: React.MutableRefObject<string | null>;
}

interface UseAudioDownloadReturn {
  isDownloadingAudio: boolean;
  downloadMessage: DownloadMessage;
  setDownloadMessage: React.Dispatch<React.SetStateAction<DownloadMessage>>;
  downloadAudioFromSecondRow: () => Promise<void>;
}

// Safe Electron API access
const safeElectronAPI = {
  getLastSavedMetadata: async (): Promise<any> => {
    if (typeof window !== "undefined" && (window as any).electronAPI?.getLastSavedMetadata) {
      return await (window as any).electronAPI.getLastSavedMetadata();
    }
    return null;
  },
  readFileBuffer: async (path: string): Promise<ArrayBuffer> => {
    if (typeof window !== "undefined" && (window as any).electronAPI?.readFileBuffer) {
      return await (window as any).electronAPI.readFileBuffer(path);
    }
    throw new Error("Electron API readFileBuffer not available");
  },
  writeFile: async (path: string, data: string): Promise<void> => {
    if (typeof window !== "undefined" && (window as any).electronAPI?.writeFile) {
      await (window as any).electronAPI.writeFile(path, data);
      return;
    }
    console.warn("Electron API writeFile not available");
    return;
  },
  writeFileBuffer: async (path: string, data: ArrayBuffer): Promise<void> => {
    if (typeof window !== "undefined" && (window as any).electronAPI?.writeFileBuffer) {
      await (window as any).electronAPI.writeFileBuffer(path, data);
      return;
    }
    console.warn("Electron API writeFileBuffer not available");
    return;
  }
};

export const useAudioDownload = ({
  overlays,
  selectedSegment,
  audioType,
  durationInFrames,
  projectUrl,
  pathRef
}: UseAudioDownloadProps): UseAudioDownloadReturn => {
  const [isDownloadingAudio, setIsDownloadingAudio] = useState<boolean>(false);
  const [lastDownloadTime, setLastDownloadTime] = useState<number>(0);
  const [downloadMessage, setDownloadMessage] = useState<DownloadMessage>({ 
    type: null, 
    message: "" 
  });

  // Helper for processing multiple segments as timeline
  const processMultipleSegmentsAsTimeline = async (
    overlaysToDownload: SoundOverlay[],
    audioContext: AudioContext,
    currentFPS: number
  ): Promise<number> => {
    const projectDurationSeconds = (durationInFrames || 0) / currentFPS;
    const sampleRate = 44100;
    const projectDurationSamples = Math.floor(projectDurationSeconds * sampleRate);
    
    const projectBuffer = audioContext.createBuffer(2, projectDurationSamples, sampleRate);
    
    // Initialize project buffer with silence (zeros) to ensure full duration
    for (let channel = 0; channel < 2; channel++) {
      const channelData = projectBuffer.getChannelData(channel);
      channelData.fill(0);
    }
    
    // Process segments in parallel where possible
    const segmentPromises = overlaysToDownload.map(async (overlay) => {
      try {
        const response = await fetch(overlay.src);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        return { overlay, audioBuffer };
      } catch (error) {
        console.error(`Error loading segment ${overlay.id}:`, error);
        return null;
      }
    });
    
    const segmentResults = await Promise.all(segmentPromises);
    
    // Mix segments into project buffer - this will maintain full timeline duration
    for (const result of segmentResults) {
      if (!result) continue;
      
      const { overlay, audioBuffer } = result;
      mixSegmentIntoProjectBuffer(
        projectBuffer, audioBuffer, overlay, currentFPS, sampleRate
      );
    }
    
    // Download the mixed result using appropriate handler
    const projectWavBlob = audioBufferToWav(projectBuffer);
    const downloadHandler = createDownloadHandler();
    
    // For AAF, we always start with a WAV file that gets converted
    const getFileExtension = () => {
      switch (audioType) {
        case 'aaf': return 'wav'; // AAF handler expects WAV input
        case 'wav': return 'wav';
        default: return audioType;
      }
    };
    const fileName = `project_audio_timeline_${Date.now()}.${getFileExtension()}`;
    
    await downloadHandler(projectWavBlob, fileName);
    
    return overlaysToDownload.length;
  };

  // Strategy for timeline-based processing (for entire row download)
  const timelineStrategy: AudioProcessingStrategy = {
    shouldUse: (_overlaysToDownload) => {
      // Use timeline strategy when downloading entire row (no specific AUDIO segment selected)
      // This ensures full timeline duration with silence padding
      const isAudioSegmentSelected = selectedSegment && 
        overlays.some(overlay => 
          overlay.id === selectedSegment.id && 
          overlay.type === OverlayType.SOUND && 
          overlay.row === 1
        );
      const isEntireRowDownload = !isAudioSegmentSelected;
      return isEntireRowDownload && (audioType === 'wav' || audioType === 'aaf');
    },
    process: processMultipleSegmentsAsTimeline
  };

  // Strategy for individual segment processing
  const individualStrategy: AudioProcessingStrategy = {
    shouldUse: () => true, // Default fallback
    process: async (overlaysToDownload, audioContext, currentFPS) => {
      let successCount = 0;
      
      // Create download handler based on audio type
      const downloadHandler = createDownloadHandler();
      
      for (const overlay of overlaysToDownload) {
        try {
          const response = await fetch(overlay.src);
          const arrayBuffer = await response.arrayBuffer();
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
          
          const segmentBuffer = extractAudioSegment(audioBuffer, overlay, currentFPS, audioContext);
          
          if (segmentBuffer) {
            const wavBlob = audioBufferToWav(segmentBuffer);
            // Generate filename - for AAF we always start with WAV that gets converted
            const getFileExtension = () => {
              switch (audioType) {
                case 'aaf': return 'wav'; // AAF handler expects WAV input
                case 'wav': return 'wav';
                default: return audioType;
              }
            };
            const fileName = `audio_segment_${overlay.id}_${Date.now()}.${getFileExtension()}`;
            
            await downloadHandler(wavBlob, fileName);
            successCount++;
          }
        } catch (error) {
          console.error(`Error processing segment ${overlay.id}:`, error);
        }
      }
      
      return successCount;
    }
  };

  // Save WAV with dialog and capture the file path for Step 2 API
  const saveWavWithDialog = async (blob: Blob, fileName: string, isaaf: boolean = false): Promise<string | null> => {
    try {
      // Always keep .wav extension since we need to save as WAV first (even for AAF)
      const dialogFileName = fileName;
      console.log(`💾 Opening save dialog for: ${dialogFileName}`);
      
      // Method 1: Electron API with dialog (prioritized for full path access)
      if (typeof window !== 'undefined' && (window as any).electronAPI?.showSaveDialog) {
        try {
          // Always prioritize WAV format since we're saving WAV data
          const filters = [
            { name: 'WAV Audio', extensions: ['wav'] },
            { name: 'All Files', extensions: ['*'] }
          ];
          
          const result = await (window as any).electronAPI.showSaveDialog({
            defaultPath: dialogFileName,
            filters: filters
          });
          
          if (!result.canceled && result.filePath) {
            // Save file to chosen path
            const arrayBuffer = await blob.arrayBuffer();
            
            // Try writeFileBuffer first, then fallback to writeFile with Buffer
            try {
              if ((window as any).electronAPI.writeFileBuffer) {
                const writeResult = await (window as any).electronAPI.writeFileBuffer(result.filePath, arrayBuffer);
                if (writeResult && writeResult.success) {
                  console.log(`✅ File saved via writeFileBuffer: ${result.filePath}`);
                  return result.filePath;
                }
              }
              
              // Fallback: Convert ArrayBuffer to Buffer and use writeFile
              const uint8Array = new Uint8Array(arrayBuffer);
              const writeResult = await (window as any).electronAPI.writeFile(result.filePath, uint8Array);
              if (writeResult && writeResult.success) {
                console.log(`✅ File saved via writeFile (Buffer): ${result.filePath}`);
                return result.filePath;
              }
              
              throw new Error('Both writeFileBuffer and writeFile failed');
              
            } catch (writeError) {
              throw new Error(`Failed to save file: ${writeError}`);
            }
          } else {
            console.log('User cancelled the Electron save dialog');
            return null;
          }
          
        } catch (error) {
          console.error('Electron save dialog failed:', error);
          throw error; // Don't fallback, throw error to caller
        }
      }
      
      // If Electron API is not available, show error
      console.error('⚠️ Electron API not available for aaf conversion');
      throw new Error('aaf conversion requires the Electron save dialog for full file path access. Please ensure you are running in the Electron environment.');
      
    } catch (error) {
      console.error('Error in saveWavWithDialog:', error);
      throw error;
    }
  };

  // Step 2: Convert WAV to aaf via API and remove old WAV file
  const convertWavToaaf = async (wavFilePath: string): Promise<void> => {
    try {
      console.log(`🔄 Converting WAV to aaf: ${wavFilePath}`);
      
      // First verify the file exists at the given path
      if (typeof window !== "undefined" && (window as any).electronAPI?.fileExists) {
        const fileExists = await (window as any).electronAPI.fileExists(wavFilePath);
        if (!fileExists) {
          throw new Error(`WAV file not found at path: ${wavFilePath}`);
        }
      }
      
      if (typeof window !== "undefined" && (window as any).electronAPI?.convertWavToAAF) {
        const result = await (window as any).electronAPI.convertWavToAAF(wavFilePath);
        
        if (result.success && result.aafPath) {
          console.log(`✅ aaf conversion successful: ${result.aafPath}`);
          
          // Remove the old WAV file after successful conversion
          try {
            if (typeof window !== "undefined" && (window as any).electronAPI?.fileExists) {
              const wavExists = await (window as any).electronAPI.fileExists(wavFilePath);
              if (wavExists) {
                // Use fs.unlink via Electron API to remove the file
                if ((window as any).electronAPI?.deleteFile) {
                  const deleteResult = await (window as any).electronAPI.deleteFile(wavFilePath);
                  if (deleteResult.success) {
                    console.log(`🗑️ Old WAV file removed: ${wavFilePath}`);
                  } else {
                    console.warn(`⚠️ Failed to delete old WAV file: ${deleteResult.error}`);
                  }
                } else {
                  console.warn('⚠️ File deletion API not available, WAV file remains');
                }
              }
            }
          } catch (deleteError) {
            console.warn(`⚠️ Could not remove old WAV file: ${deleteError}`);
          }
          
          setDownloadMessage({
            type: "success",
            message: `AAF audio download successfully`
          });
        } else {
          throw new Error(result.error || 'Conversion failed');
        }
      } else {
        throw new Error('Electron API not available');
      }
      
    } catch (error: unknown) {
      console.error('❌ Failed to convert WAV to aaf:', error);
      setDownloadMessage({
        type: "error",
        message: `❌ Step 2 Failed: Could not convert to aaf. Error: ${(error as Error).message}. WAV file saved successfully at: ${wavFilePath}`
      });
    }
  };

  // Factory function to create appropriate download handler
  const createDownloadHandler = () => {
    if (audioType === 'wav') {
      // WAV: Use Electron save dialog for better control
      return async (blob: Blob, fileName: string) => {
        console.log(`📥 WAV download starting for: ${fileName}`);
        
        try {
          // Try to use Electron save dialog first
          if (typeof window !== 'undefined' && (window as any).electronAPI?.showSaveDialog) {
            const result = await (window as any).electronAPI.showSaveDialog({
              defaultPath: fileName,
              filters: [
                { name: 'WAV Audio', extensions: ['wav'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            });
            
            if (!result.canceled && result.filePath) {
              // Save file to chosen path
              const arrayBuffer = await blob.arrayBuffer();
              
              try {
                if ((window as any).electronAPI.writeFileBuffer) {
                  const writeResult = await (window as any).electronAPI.writeFileBuffer(result.filePath, arrayBuffer);
                  if (writeResult && writeResult.success) {
                    console.log(`✅ WAV file saved via writeFileBuffer: ${result.filePath}`);
                    setDownloadMessage({
                      type: "success",
                      message: `WAV audio download successfully`
                    });
                    return;
                  }
                }
                
                // Fallback to writeFile
                const uint8Array = new Uint8Array(arrayBuffer);
                const writeResult = await (window as any).electronAPI.writeFile(result.filePath, uint8Array);
                if (writeResult && writeResult.success) {
                  console.log(`✅ WAV file saved via writeFile: ${result.filePath}`);
                  setDownloadMessage({
                    type: "success",
                    message: `WAV audio download successfully`
                  });
                  return;
                }
                
                throw new Error('Failed to write WAV file');
                
              } catch (writeError) {
                throw new Error(`Failed to save WAV file: ${writeError}`);
              }
            } else {
              console.log('User cancelled WAV save dialog');
              return;
            }
          } else {
            // Fallback to browser download if Electron API not available
            console.log('⚠️ Electron API not available, falling back to browser download');
            downloadBlob(blob, fileName);
            console.log(`✅ WAV blob download triggered for: ${fileName}`);
          }
        } catch (error) {
          console.error('Error in WAV save process:', error);
          // Final fallback to browser download
          downloadBlob(blob, fileName);
          console.log(`✅ WAV blob download triggered for: ${fileName} (fallback)`);
        }
      };
    } else if (audioType === 'aaf') {
      // aaf: Step 1 + Step 2 - Save WAV with dialog and capture path for API conversion
      return async (blob: Blob, fileName: string) => {
        const wavFileName = fileName.replace('.aaf', '.wav');
        console.log(`🔄 AAF Step 1: Saving WAV file with dialog to capture path`);
        
        try {
          // Use Electron save dialog to get full file path (isaaf = true)
          const savedFilePath = await saveWavWithDialog(blob, wavFileName, true);
          
          if (savedFilePath) {
            console.log(`✅ Step 1 Complete: WAV saved to ${savedFilePath}`);
            console.log(`🔄 Step 2: Starting API conversion to aaf...`);
            
            await convertWavToaaf(savedFilePath);
          } else {
            // User cancelled the dialog
            console.log('⚠️ User cancelled aaf save dialog');
            return;
          }
        } catch (error: unknown) {
          console.error('Error in aaf process:', error);
          setDownloadMessage({
            type: "error",
            message: `❌ aaf Process Failed: ${(error as Error).message}`
          });
          // Fallback to regular download if dialog fails
          downloadBlob(blob, wavFileName);
        }
      };
    } else {
      // Default fallback for other audio types
      return (blob: Blob, fileName: string) => saveAudioToProjectFolder(blob, fileName);
    }
  };

  // Strategy selector function
  const selectProcessingStrategy = (overlaysToDownload: SoundOverlay[]): AudioProcessingStrategy => {
    const strategies = [timelineStrategy, individualStrategy];
    return strategies.find(strategy => strategy.shouldUse(overlaysToDownload)) || individualStrategy;
  };

  // Optimized audio segment extraction
  const extractAudioSegment = (
    audioBuffer: AudioBuffer,
    overlay: SoundOverlay,
    currentFPS: number,
    audioContext: AudioContext
  ): AudioBuffer | null => {
    const durationSeconds = overlay.durationInFrames / currentFPS;
    const sourceStartTimeSeconds = overlay.startFromSound || 0;
    const startSample = Math.floor(sourceStartTimeSeconds * audioBuffer.sampleRate);
    const lengthSamples = Math.floor(durationSeconds * audioBuffer.sampleRate);
    
    const maxAvailableSamples = Math.max(0, audioBuffer.length - startSample);
    const actualLengthSamples = Math.min(lengthSamples, maxAvailableSamples);
    
    if (actualLengthSamples <= 0) {
      console.warn(`No audio data available for segment ${overlay.id}`);
      return null;
    }
    
    const segmentBuffer = audioContext.createBuffer(
      audioBuffer.numberOfChannels,
      actualLengthSamples,
      audioBuffer.sampleRate
    );

    // Optimized channel copying
    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
      const sourceData = audioBuffer.getChannelData(channel);
      const segmentData = segmentBuffer.getChannelData(channel);
      
      // Use efficient subarray copying
      const sourceSubarray = sourceData.subarray(startSample, startSample + actualLengthSamples);
      segmentData.set(sourceSubarray);
    }

    return segmentBuffer;
  };

  // Helper for mixing segments into project buffer
  const mixSegmentIntoProjectBuffer = (
    projectBuffer: AudioBuffer,
    audioBuffer: AudioBuffer,
    overlay: SoundOverlay,
    currentFPS: number,
    sampleRate: number
  ): void => {
    const segmentStartTimeSeconds = overlay.from / currentFPS;
    const projectStartSample = Math.floor(segmentStartTimeSeconds * sampleRate);
    
    const sourceStartTimeSeconds = overlay.startFromSound || 0;
    const sourceStartSample = Math.floor(sourceStartTimeSeconds * audioBuffer.sampleRate);
    
    // Calculate how many samples we can copy from source
    const availableSourceSamples = audioBuffer.length - sourceStartSample;
    const maxProjectSamples = projectBuffer.length - projectStartSample;
    
    // Use the segment duration from overlay, but cap it to available samples
    const segmentDurationSeconds = overlay.durationInFrames / currentFPS;
    const requestedLengthSamples = Math.floor(segmentDurationSeconds * sampleRate);
    const actualLengthSamples = Math.min(requestedLengthSamples, availableSourceSamples, maxProjectSamples);
    
    if (actualLengthSamples <= 0 || projectStartSample >= projectBuffer.length) {
      console.warn(`Skipping segment ${overlay.id}: no valid samples to copy`);
      return;
    }
    
    const numChannels = Math.min(audioBuffer.numberOfChannels, projectBuffer.numberOfChannels);
    
    // Simple 1:1 sample copying (assuming both buffers have same sample rate)
    for (let channel = 0; channel < numChannels; channel++) {
      const sourceChannelData = audioBuffer.getChannelData(channel);
      const projectChannelData = projectBuffer.getChannelData(channel);
      
      for (let i = 0; i < actualLengthSamples; i++) {
        const sourceIndex = sourceStartSample + i;
        const projectIndex = projectStartSample + i;
        
        if (sourceIndex < audioBuffer.length && projectIndex < projectBuffer.length) {
          // Mix audio (add to existing content, allowing for overlaps)
          projectChannelData[projectIndex] += sourceChannelData[sourceIndex];
        }
      }
    }
    
    console.log(`Mixed segment ${overlay.id}: ${actualLengthSamples} samples at project position ${projectStartSample}`);
  };

  // Helper for blob download
  const downloadBlob = (blob: Blob, fileName: string): void => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Optimized helper function to save audio file to project folder
  const saveAudioToProjectFolder = async (audioBlob: Blob, fileName: string): Promise<void> => {
    const getProjectDirectory = async (): Promise<string> => {
      // Priority order for project directory detection
      const strategies = [
        () => projectUrl,
        () => pathRef?.current,
        async () => {
          const metadata = await safeElectronAPI.getLastSavedMetadata();
          return metadata?.metadata?.saveDir || metadata?.saveDir || 
                 (metadata?.filePath ? path.dirname(metadata.filePath) : null);
        },
        () => './MochaChai_Audio_Downloads'
      ];
      
      for (const strategy of strategies) {
        try {
          const dir = await strategy();
          if (dir) return dir;
        } catch (error) {
          console.warn('Strategy failed:', error);
        }
      }
      
      throw new Error('Unable to determine project directory');
    };

    const saveStrategies = [
      // Primary: Direct buffer write
      async (filePath: string, arrayBuffer: ArrayBuffer) => {
        if (typeof window !== 'undefined' && (window as any).electronAPI?.writeFileBuffer) {
          await (window as any).electronAPI.writeFileBuffer(filePath, arrayBuffer);
          return true;
        }
        return false;
      },
      // Fallback: Hex encoding (more efficient than base64)
      async (filePath: string, arrayBuffer: ArrayBuffer) => {
        const uint8Array = new Uint8Array(arrayBuffer);
        const hexString = Array.from(uint8Array, byte => 
          byte.toString(16).padStart(2, '0')
        ).join('');
        
        const content = `WAV_HEX_DATA:${hexString}\\n\\nHex-encoded WAV data for: ${fileName}`;
        const hexFilePath = filePath.replace(/\\.wav$/, '_hexdata.wav');
        await safeElectronAPI.writeFile(hexFilePath, content);
        return true;
      }
    ];

    try {
      const projectDir = await getProjectDirectory();
      const audioFileName = `downloaded_${fileName}`;
      const filePath = path.join(projectDir, audioFileName);
      const arrayBuffer = await audioBlob.arrayBuffer();
      
      for (let i = 0; i < saveStrategies.length; i++) {
        try {
          const success = await saveStrategies[i](filePath, arrayBuffer);
          if (success) {
            console.log(`✅ Audio saved using strategy ${i + 1} to: ${filePath}`);
            return;
          }
        } catch (error) {
          console.warn(`Strategy ${i + 1} failed:`, error);
        }
      }
      
      throw new Error('All save strategies failed');
    } catch (error) {
      console.error('Error saving audio to project folder:', error);
      throw error;
    }
  };

  // Standard WAV file format converter
  const audioBufferToWav = (buffer: AudioBuffer): Blob => {
    if (!buffer || buffer.length === 0) {
      throw new Error('Invalid or empty audio buffer');
    }

    const numChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const length = buffer.length;
    
    console.log(`🎵 Creating WAV: ${numChannels}ch, ${sampleRate}Hz, ${length} samples`);
    
    // Calculate buffer sizes
    const bytesPerSample = 2; // 16-bit
    const blockAlign = numChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;
    const dataSize = length * blockAlign;
    const fileSize = 36 + dataSize;
    
    // Create array buffer
    const arrayBuffer = new ArrayBuffer(44 + dataSize);
    const view = new DataView(arrayBuffer);
    
    // Helper to write string
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    // Write WAV header
    writeString(0, 'RIFF');                    // ChunkID
    view.setUint32(4, fileSize, true);         // ChunkSize (little-endian)
    writeString(8, 'WAVE');                    // Format
    writeString(12, 'fmt ');                   // Subchunk1ID
    view.setUint32(16, 16, true);              // Subchunk1Size (16 for PCM)
    view.setUint16(20, 1, true);               // AudioFormat (1 for PCM)
    view.setUint16(22, numChannels, true);     // NumChannels
    view.setUint32(24, sampleRate, true);      // SampleRate
    view.setUint32(28, byteRate, true);        // ByteRate
    view.setUint16(32, blockAlign, true);      // BlockAlign
    view.setUint16(34, 16, true);              // BitsPerSample (16-bit)
    writeString(36, 'data');                   // Subchunk2ID
    view.setUint32(40, dataSize, true);        // Subchunk2Size
    
    // Write audio data (interleaved)
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        const sample = buffer.getChannelData(channel)[i];
        const intSample = Math.max(-32768, Math.min(32767, Math.floor(sample * 32767)));
        view.setInt16(offset, intSample, true);
        offset += 2;
      }
    }
    
    const wavBlob = new Blob([arrayBuffer], { type: 'audio/wav' });
    console.log(`✅ WAV created: ${wavBlob.size} bytes (expected: ${44 + dataSize})`);
    
    return wavBlob;
  };

  // Helper for download completion handling
  const handleDownloadCompletion = async (successfulDownloads: number): Promise<void> => {
    if (successfulDownloads === 0) {
      setDownloadMessage({
        type: "error",
        message: "No audio files were processed. Please check if audio exists in the second row."
      });
    }
    // Note: Both WAV and aaf downloads now handle their own success messages
    // WAV downloads show success after file is saved via Electron dialog
    // aaf downloads show success after successful conversion
  };

  // Main download function
  const downloadAudioFromSecondRow = useCallback(async (): Promise<void> => {
    const now = Date.now();
    
    // Debounce mechanism
    if (isDownloadingAudio || now - lastDownloadTime < 2000) {
      console.log("🚫 Download ignored - too frequent or already in progress");
      return;
    }
    
    const processId = Math.random().toString(36).substring(7);
    console.log(`🚀 Starting download process (ID: ${processId})`);
    
    setIsDownloadingAudio(true);
    setLastDownloadTime(now);
    
    // Clear any previous download messages
    setDownloadMessage({ type: null, message: "" });
    
    try {
      // Get overlays to download
      const secondRowAudioOverlays = overlays.filter(
        overlay => overlay.type === OverlayType.SOUND && overlay.row === 1
      ) as SoundOverlay[];

      if (secondRowAudioOverlays.length === 0) {
        setDownloadMessage({
          type: "error",
          message: "❌ No audio found in second row"
        });
        return;
      }

      const selectedAudioOverlays = secondRowAudioOverlays.filter(
        overlay => selectedSegment && overlay.id === selectedSegment.id
      );

      const overlaysToDownload = selectedAudioOverlays.length > 0 
        ? selectedAudioOverlays 
        : secondRowAudioOverlays;

      console.log(`📥 Processing ${overlaysToDownload.length} segments as ${audioType}`);

      let successfulDownloads = 0;
      const currentFPS = getEffectiveFPS(overlays) || 30;

      // Shared audio context for better performance
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      try {
        // Use strategy pattern to eliminate nested conditionals
        const strategy = selectProcessingStrategy(overlaysToDownload);
        successfulDownloads = await strategy.process(overlaysToDownload, audioContext, currentFPS);
      } finally {
        // Always clean up audio context
        audioContext.close();
      }
      
      console.log(`🏁 Process completed (ID: ${processId}). Success: ${successfulDownloads}/${overlaysToDownload.length}`);
      
      await handleDownloadCompletion(successfulDownloads);
      
    } catch (error) {
      console.error("Error in downloadAudioFromSecondRow:", error);
      setDownloadMessage({
        type: "error",
        message: "❌ Error saving audio files. Please check console for details."
      });
    } finally {
      setIsDownloadingAudio(false);
    }
  }, [
    isDownloadingAudio, 
    lastDownloadTime, 
    overlays, 
    selectedSegment, 
    audioType, 
    durationInFrames,
    selectProcessingStrategy
  ]);

  return {
    isDownloadingAudio,
    downloadMessage,
    setDownloadMessage,
    downloadAudioFromSecondRow
  };
};