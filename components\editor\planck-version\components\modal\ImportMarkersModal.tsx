import {
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material";
import CustomModal from "../CustomModal";
import Button from "../Button";
import { CheckedIcon, UncheckedIcon } from "../icon/CustomIcon";
import { useProjectManager } from "@/store/modalStore";
import { useState, useRef, useCallback } from "react";
import { useSegmentStore } from "@/store/segmentStore";
import { useCutStore } from "@/store/cutStore";
// Local implementation to avoid import issues
const setXmlFpsOverride = (fps: number | null): void => {
  console.log(`🎬 Timeline FPS override set to: ${fps || "none"}`);
};

const ImportMarkersModal = () => {
  const { modalImportMedia, closeImportMarkersModal, currentVideoFPS } =
    useProjectManager();
  const [importType, setImportType] = useState<string>("fcpxml");
  const [showNoCutPointsWarning, setShowNoCutPointsWarning] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const setTimeFrameSegment = useSegmentStore(
    (state) => state.setTimeFrameSegment
  );
  const addCutTime = useCutStore((state) => state.addCutTime);
  const setCutTimes = useCutStore((state) => state.setCutTimes);
  const cutTimes = useCutStore((state) => state.cutTimes);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const getAcceptAttribute = () => {
    switch (importType) {
      case "xml":
        return ".xml";
      case "fcpxml":
        // Include both .fcpxml and .fcpxmld files
        return ".fcpxml,.fcpxmld";
      case "edl":
        return ".edl";
      default:
        return ".xml,.fcpxml,.edl,.fcpxmld";
    }
  };

  const handleImportMedia = () => {
    if (
      importType === "xml" ||
      importType === "fcpxml" ||
      importType === "edl"
    ) {
      // Trigger file input click
      fileInputRef.current?.click();
    } else {
      closeImportMarkersModal();
    }
  };

  const handleCancel = useCallback(() => {
    // Abort any ongoing processing
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsProcessing(false);
    }

    // Clear FPS override when canceling
    setXmlFpsOverride(null);
    closeImportMarkersModal();
  }, [closeImportMarkersModal]);

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Start processing state
    setIsProcessing(true);
    abortControllerRef.current = new AbortController();

    try {
      // Single file selection mode
      const file = files[0];

      // Check if this is a .fcpxmld file that needs to be processed as a bundle
      if (
        file.name.toLowerCase().endsWith(".fcpxmld") ||
        file.name.toLowerCase().includes(".fcpxmld")
      ) {
        await processFcpxmldBundle(file, abortControllerRef.current.signal);
      } else {
        await processXmlFile(file, abortControllerRef.current.signal);
      }

      if (!abortControllerRef.current.signal.aborted) {
        closeImportMarkersModal();
      }
    } catch (error) {
      if (error instanceof Error && error.name !== "AbortError") {
        console.error("Error importing file:", error);
        // Show error to user
        setShowNoCutPointsWarning(true);
      }
    } finally {
      // Clear processing state
      setIsProcessing(false);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      abortControllerRef.current = null;
    }
  };

  const processFcpxmldBundle = async (file: File, signal?: AbortSignal) => {
    try {
      // .fcpxmld files are actually ZIP archives containing XML files
      // We need to extract and find the main .fcpxml file inside

      // First, try to read as zip file using File API and ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();

      if (signal?.aborted) {
        throw new DOMException("Operation aborted", "AbortError");
      }

      // Import JSZip dynamically to handle the ZIP extraction
      const JSZip = await import("jszip");
      const zip = new JSZip.default();

      try {
        const zipFile = await zip.loadAsync(arrayBuffer);

        if (signal?.aborted) {
          throw new DOMException("Operation aborted", "AbortError");
        }

        // Log all files in the ZIP for debugging
        const allFiles = Object.keys(zipFile.files);

        // Look for .fcpxml files inside the zip
        const fcpxmlFiles = allFiles.filter(
          (filename) =>
            filename.toLowerCase().endsWith(".fcpxml") &&
            !filename.startsWith("__MACOSX/") &&
            !zipFile.files[filename].dir // Ensure it's not a directory
        );

        if (fcpxmlFiles.length === 0) {
          // Try to find any XML files as fallback
          const xmlFiles = allFiles.filter(
            (filename) =>
              filename.toLowerCase().endsWith(".xml") &&
              !filename.startsWith("__MACOSX/") &&
              !zipFile.files[filename].dir
          );
          if (xmlFiles.length > 0) {
            fcpxmlFiles.push(...xmlFiles);
          }
        }

        if (fcpxmlFiles.length === 0) {
          throw new Error(
            "No .fcpxml or .xml files found inside the .fcpxmld bundle"
          );
        }

        // Use the first .fcpxml file found (usually there's only one main file)
        const fcpxmlFilename = fcpxmlFiles[0];
        const fcpxmlFile = zipFile.files[fcpxmlFilename];

        if (!fcpxmlFile) {
          throw new Error(`Could not access file: ${fcpxmlFilename}`);
        }

        if (fcpxmlFile.dir) {
          throw new Error(
            `Selected item is a directory, not a file: ${fcpxmlFilename}`
          );
        }

        // Extract the XML content

        const xmlContent = await fcpxmlFile.async("text");
        if (signal?.aborted) {
          throw new DOMException("Operation aborted", "AbortError");
        }

        // Parse the extracted XML content using existing logic
        const { cutPoints, xmlFps } = await extractCutPointsFromXml(
          xmlContent,
          signal,
          fcpxmlFilename
        );

        if (cutPoints.length > 0) {
          await applyCutPoints(cutPoints, xmlFps, signal);
        } else {
          // Show the warning to user
          setShowNoCutPointsWarning(true);
        }
      } catch (zipError) {
        // If ZIP extraction fails, try to read as plain text (fallback)
        const textContent = new TextDecoder().decode(arrayBuffer);
        await processXmlFile(
          new File([textContent], file.name.replace(".fcpxmld", ".fcpxml"), {
            type: "text/xml",
          }),
          signal
        );
      }
    } catch (error) {
      throw error;
    }
  };

  const processXmlFile = async (file: File, signal?: AbortSignal) => {
    try {
      // Read file content
      const fileContent = await file.text();

      if (signal?.aborted)
        throw new DOMException("Operation aborted", "AbortError");

      // Parse file and extract cut points with FPS information
      const { cutPoints, xmlFps } = await extractCutPointsFromXml(
        fileContent,
        signal,
        file.name
      );

      if (cutPoints.length > 0) {
        // Convert cut points to HH:MM:SS.FF format and trigger splits
        await applyCutPoints(cutPoints, xmlFps, signal);
      } else {
        console.log("No cut points found in imported file");
      }
    } catch (error) {
      console.error("Error processing file:", error);
    }
  };

  const extractCutPointsFromXml = async (
    xmlContent: string,
    signal?: AbortSignal,
    fileName?: string
  ): Promise<{
    cutPoints: Array<{ timeInSeconds: number; originalFrame: number }>;
    xmlFps: number;
  }> => {
    const cutPoints: Array<{ timeInSeconds: number; originalFrame: number }> =
      [];
    let xmlFps = 30; // Default FPS

    try {
      // Parse XML content in chunks to avoid blocking UI
      await new Promise((resolve) => setTimeout(resolve, 0));
      if (signal?.aborted)
        throw new DOMException("Operation aborted", "AbortError");

      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlContent, "text/xml");

      // Detect file type based on root element, filename, or content
      const isEdl =
        fileName?.toLowerCase().endsWith(".edl") ||
        !xmlContent.trim().startsWith("<");

      // Enhanced FCPXML detection
      const isFcpxml =
        xmlDoc.querySelector("fcpxml") !== null ||
        fileName?.toLowerCase().includes("fcpxml") ||
        fileName?.toLowerCase().includes("fcpxmld") ||
        xmlContent.includes("<fcpxml") ||
        xmlContent.includes("fcpxml") ||
        xmlDoc.documentElement?.tagName?.toLowerCase() === "fcpxml";

      if (isEdl) {
        // Handle EDL format (plain text, not XML)
        const result = await extractFromEdl(xmlContent, signal);
        return removeDuplicatesAndValidate(result.cutPoints, result.xmlFps);
      } else if (isFcpxml) {
        // Handle FCPXML format
        const result = await extractFromFcpxml(xmlDoc, signal);
        return removeDuplicatesAndValidate(result.cutPoints, result.xmlFps);
      } else {
        // Handle traditional XML format
        const result = await extractFromTraditionalXml(xmlDoc, signal);
        return removeDuplicatesAndValidate(result.cutPoints, result.xmlFps);
      }
    } catch (error) {
      console.error("Error parsing XML:", error);
    }

    return { cutPoints: [], xmlFps: 30 };
  };

  const parseTimeAttribute = (timeAttr: string): number => {
    // Parse FCPXML time formats like "13000/2400s", "56s", "30438400/307200s"
    if (timeAttr.includes("/")) {
      // Fractional format like "13000/2400s"
      const match = timeAttr.match(/(\d+)\/(\d+)s/);
      if (match) {
        const numerator = parseInt(match[1]);
        const denominator = parseInt(match[2]);
        const result = numerator / denominator;
        return result;
      }
    } else {
      // Simple format like "56s"
      const match = timeAttr.match(/(\d+(?:\.\d+)?)s?/);
      if (match) {
        const result = parseFloat(match[1]);
        return result;
      }
    }
    return 0;
  };

  const removeDuplicatesAndValidate = (
    cutPoints: Array<{ timeInSeconds: number; originalFrame: number }>,
    xmlFps: number
  ): {
    cutPoints: Array<{ timeInSeconds: number; originalFrame: number }>;
    xmlFps: number;
  } => {
    // Remove duplicates with a small tolerance for floating point precision
    const tolerance = 0.001; // 1ms tolerance
    const uniqueCutPoints: Array<{
      timeInSeconds: number;
      originalFrame: number;
    }> = [];
    const sortedCutPoints = [...cutPoints].sort(
      (a, b) => a.timeInSeconds - b.timeInSeconds
    );

    for (const point of sortedCutPoints) {
      const isDuplicate = uniqueCutPoints.some(
        (existing) =>
          Math.abs(existing.timeInSeconds - point.timeInSeconds) < tolerance
      );
      if (!isDuplicate) {
        uniqueCutPoints.push(point);
      }
    }

    // Show popup warning if no cut points found
    if (uniqueCutPoints.length < 1) {
      setShowNoCutPointsWarning(true);
    }

    return { cutPoints: uniqueCutPoints, xmlFps };
  };

  const extractFromFcpxml = async (
    xmlDoc: Document,
    signal?: AbortSignal
  ): Promise<{
    cutPoints: Array<{ timeInSeconds: number; originalFrame: number }>;
    xmlFps: number;
  }> => {
    const cutPoints: Array<{ timeInSeconds: number; originalFrame: number }> =
      [];
    let xmlFps = currentVideoFPS || 24; // FCPXML default

    try {
      // Get FPS from format element - FCPXML uses frameDuration like "100/2400s" which means 24fps
      const formatElement = xmlDoc.querySelector("format");
      if (formatElement) {
        const frameDuration = formatElement.getAttribute("frameDuration");
        if (frameDuration) {
          // Parse frameDuration like "100/2400s"
          const match = frameDuration.match(/(\d+)\/(\d+)s/);
          if (match) {
            const numerator = parseInt(match[1]);
            const denominator = parseInt(match[2]);
            xmlFps = denominator / numerator; // 2400/100 = 24fps
          }
        }
      } else {
        console.log("⚠️ No format element found, using default FPS:", xmlFps);
      }

      // Set timeline FPS override to match FCPXML FPS for dynamic timeline
      setXmlFpsOverride(xmlFps);

      // 1. Asset clips (video/audio clips)
      const assetClips = xmlDoc.querySelectorAll("asset-clip");

      // 2. Clips (general clips)
      const clips = xmlDoc.querySelectorAll("clip");

      // 3. Video/Audio clips
      const videoClips = xmlDoc.querySelectorAll("video");
      const audioClips = xmlDoc.querySelectorAll("audio");

      // 4. Markers
      const markers = xmlDoc.querySelectorAll("marker");

      // 5. Spine elements (main story)
      const spines = xmlDoc.querySelectorAll("spine");

      const allElements = [
        ...Array.from(assetClips),
        ...Array.from(clips),
        ...Array.from(videoClips),
        ...Array.from(audioClips),
        ...Array.from(markers),
      ];

      const rawCutPoints: Array<{
        timeInSeconds: number;
        originalFrame: number;
      }> = [];

      // Process all elements in batches
      const batchSize = 100;
      for (let i = 0; i < allElements.length; i += batchSize) {
        if (signal?.aborted)
          throw new DOMException("Operation aborted", "AbortError");

        const batch = allElements.slice(i, i + batchSize);
        batch.forEach((element, batchIndex) => {
          const index = i + batchIndex;

          // Get various time attributes
          const offsetAttr = element.getAttribute("offset");
          const startAttr = element.getAttribute("start");
          const durationAttr = element.getAttribute("duration");
          const tcStartAttr = element.getAttribute("tcStart");
          const tcFormatAttr = element.getAttribute("tcFormat");

          // Try different time attributes
          const timeAttrs = [offsetAttr, startAttr, tcStartAttr].filter(
            Boolean
          );

          timeAttrs.forEach((timeAttr) => {
            if (timeAttr) {
              // Parse time format like "13000/2400s" or "56s"
              const timeSeconds = parseTimeAttribute(timeAttr);

              if (timeSeconds > 0.001) {
                // Skip very first start which is usually 0
                const framePos = Math.floor(timeSeconds * xmlFps);
                const cutPoint = {
                  timeInSeconds: timeSeconds,
                  originalFrame: framePos,
                };
                rawCutPoints.push(cutPoint);
                cutPoints.push(cutPoint);
              }
            }
          });
        });

        // Yield control to prevent UI blocking
        await new Promise((resolve) => setTimeout(resolve, 0));
      }
    } catch (error) {
      console.error("❌ Error parsing FCPXML:", error);
    }

    return { cutPoints, xmlFps };
  };

  const extractFromTraditionalXml = async (
    xmlDoc: Document,
    signal?: AbortSignal
  ): Promise<{
    cutPoints: Array<{ timeInSeconds: number; originalFrame: number }>;
    xmlFps: number;
  }> => {
    const cutPoints: Array<{ timeInSeconds: number; originalFrame: number }> =
      [];
    let xmlFps = 30; // Default FPS

    try {
      // Get the timebase (FPS) from the XML for proper conversion
      const rateElement = xmlDoc.querySelector("rate timebase");
      xmlFps = rateElement ? parseInt(rateElement.textContent || "30") : 30;

      // Set timeline FPS override to match XML FPS for dynamic timeline
      setXmlFpsOverride(xmlFps);

      // For XMEML files, look for clipitem elements with start/end values
      const clipItems = xmlDoc.querySelectorAll("clipitem");

      const rawCutPoints: Array<{
        timeInSeconds: number;
        originalFrame: number;
      }> = [];

      // Process clipItems in batches to avoid blocking
      const batchSize = 100;
      for (let i = 0; i < clipItems.length; i += batchSize) {
        if (signal?.aborted)
          throw new DOMException("Operation aborted", "AbortError");

        const batch = Array.from(clipItems).slice(i, i + batchSize);
        batch.forEach((clipItem, batchIndex) => {
          const index = i + batchIndex;
          const startElement = clipItem.querySelector("start");
          const endElement = clipItem.querySelector("end");

          if (startElement && endElement) {
            const startFrames = parseInt(startElement.textContent || "0");
            const endFrames = parseInt(endElement.textContent || "0");

            // Convert frames to seconds using the XML's FPS
            const startSeconds = startFrames / xmlFps;
            const endSeconds = endFrames / xmlFps;

            // Format the timecodes for display
            const startTimecode = formatTimeWithFrames(startSeconds, xmlFps);
            const endTimecode = formatTimeWithFrames(endSeconds, xmlFps);

            // Only add start times as cut points (skip very first start which is usually 0)
            if (startSeconds > 0.001) {
              const cutPoint = {
                timeInSeconds: startSeconds,
                originalFrame: startFrames,
              };
              rawCutPoints.push(cutPoint);
              cutPoints.push(cutPoint);
            }
          }
        });

        // Yield control to prevent UI blocking
        await new Promise((resolve) => setTimeout(resolve, 0));
      }

      // Also look for other common time elements as fallback
      const timeElements = xmlDoc.querySelectorAll(
        "time, cutpoint, split, timecode"
      );
      timeElements.forEach((element) => {
        const timeValue = element.textContent?.trim();
        if (timeValue) {
          const seconds = parseTimeToSeconds(timeValue, xmlFps);
          if (seconds !== null && seconds >= 0) {
            const framePos = Math.floor(seconds * xmlFps);
            cutPoints.push({ timeInSeconds: seconds, originalFrame: framePos });
          }
        }
      });

      // Look for time attributes as additional fallback
      const elementsWithTimeAttrs = xmlDoc.querySelectorAll(
        "[time], [start], [timecode], [cutpoint]"
      );
      elementsWithTimeAttrs.forEach((element) => {
        const timeAttrs = ["time", "start", "timecode", "cutpoint"];
        timeAttrs.forEach((attr) => {
          const timeValue = element.getAttribute(attr);
          if (timeValue) {
            const seconds = parseTimeToSeconds(timeValue, xmlFps);
            if (seconds !== null && seconds >= 0) {
              const framePos = Math.floor(seconds * xmlFps);
              cutPoints.push({
                timeInSeconds: seconds,
                originalFrame: framePos,
              });
            }
          }
        });
      });
    } catch (error) {
      console.error("Error parsing XML:", error);
    }

    // Remove duplicates with a small tolerance for floating point precision
    const tolerance = 0.001; // 1ms tolerance
    const uniqueCutPoints: Array<{
      timeInSeconds: number;
      originalFrame: number;
    }> = [];
    const sortedCutPoints = [...cutPoints].sort(
      (a, b) => a.timeInSeconds - b.timeInSeconds
    );
    const duplicatesRemoved: Array<{
      timeInSeconds: number;
      originalFrame: number;
    }> = [];

    for (const point of sortedCutPoints) {
      const isDuplicate = uniqueCutPoints.some(
        (existing) =>
          Math.abs(existing.timeInSeconds - point.timeInSeconds) < tolerance
      );
      if (!isDuplicate) {
        uniqueCutPoints.push(point);
      } else {
        duplicatesRemoved.push(point);
      }
    }

    // Show popup warning if no cut points found
    if (uniqueCutPoints.length < 1) {
      setShowNoCutPointsWarning(true);
    }

    return { cutPoints: uniqueCutPoints, xmlFps };
  };

  const extractFromEdl = async (
    edlContent: string,
    signal?: AbortSignal
  ): Promise<{
    cutPoints: Array<{ timeInSeconds: number; originalFrame: number }>;
    xmlFps: number;
  }> => {
    const cutPoints: Array<{ timeInSeconds: number; originalFrame: number }> =
      [];

    // Get dynamic FPS from the modal store, fallback to 24fps
    let xmlFps = currentVideoFPS || 24;

    // Set timeline FPS override immediately
    setXmlFpsOverride(xmlFps);

    try {
      // Split EDL content into lines
      const lines = edlContent.split("\n").filter((line) => line.trim());

      // Look for frame rate in header - only override if explicitly needed
      let foundFCMHeader = false;
      for (const line of lines) {
        if (line.includes("FCM:")) {
          foundFCMHeader = true;

          if (line.includes("DROP FRAME") && !line.includes("NON-DROP FRAME")) {
            xmlFps = 29.97; // Drop frame override - this is definitive from EDL
          }
        }
      }

      // If no FCM header found and no effective FPS, use EDL default
      if (!foundFCMHeader && !currentVideoFPS) {
        xmlFps = 24; // Default for EDL files
      }

      // Process lines in batches to avoid blocking
      const batchSize = 100;
      for (let i = 0; i < lines.length; i += batchSize) {
        if (signal?.aborted)
          throw new DOMException("Operation aborted", "AbortError");

        const batch = lines.slice(i, i + batchSize);
        batch.forEach((line) => {
          // Handle the new EDL format:
          // 001  AX       V     C        00:00:00:00 00:00:05:10 00:00:00:00 00:00:05:10
          // Track# Reel    Type  Edit     Source In   Source Out  Record In   Record Out

          // Match enhanced EDL format with track and reel info
          const edlMatch = line.match(
            /^\d+\s+\w+\s+[VA]+\s+C\s+(\d{2}:\d{2}:\d{2}:\d{2})\s+(\d{2}:\d{2}:\d{2}:\d{2})\s+(\d{2}:\d{2}:\d{2}:\d{2})\s+(\d{2}:\d{2}:\d{2}:\d{2})/
          );

          if (edlMatch) {
            // Extract record in time (timeline position)
            const [, , , recordIn] = edlMatch;

            // Use record in time as cut point (timeline position)
            const recordInSeconds = parseTimeToSeconds(recordIn, xmlFps);
            if (recordInSeconds !== null && recordInSeconds > 0.001) {
              // For EDL, calculate the exact frame number from the timecode directly
              const [, hours, minutes, seconds, frames] =
                recordIn.match(/(\d{2}):(\d{2}):(\d{2}):(\d{2})/) || [];
              const exactFramePos =
                parseInt(hours) * 3600 * xmlFps +
                parseInt(minutes) * 60 * xmlFps +
                parseInt(seconds) * xmlFps +
                parseInt(frames);

              cutPoints.push({
                timeInSeconds: recordInSeconds,
                originalFrame: exactFramePos,
              });
            }
          } else {
            // Fallback: Look for standalone timecodes in various formats
            const timecodes = line.match(/\d{2}:\d{2}:\d{2}[:\.]?\d{0,2}/g);
            if (timecodes) {
              timecodes.forEach((timecode) => {
                const seconds = parseTimeToSeconds(timecode, xmlFps);
                if (seconds !== null && seconds > 0.001) {
                  const framePos = Math.floor(seconds * xmlFps);
                  cutPoints.push({
                    timeInSeconds: seconds,
                    originalFrame: framePos,
                  });
                }
              });
            }
          }
        });

        // Yield control to prevent UI blocking
        await new Promise((resolve) => setTimeout(resolve, 0));
      }

      // Set timeline FPS override to match EDL FPS for dynamic timeline
      setXmlFpsOverride(xmlFps);
    } catch (error) {
      console.error("Error parsing EDL:", error);
    }

    return { cutPoints, xmlFps };
  };

  const parseTimeToSeconds = (
    timeStr: string,
    xmlFps: number = 30
  ): number | null => {
    // Handle various time formats: HH:MM:SS:FF, HH:MM:SS.FF, HH:MM:SS, MM:SS, or plain seconds
    const timeRegexes = [
      /^(\d{1,2}):(\d{1,2}):(\d{1,2}):(\d{1,3})$/, // HH:MM:SS:FF (Final Cut Pro format)
      /^(\d{1,2}):(\d{1,2}):(\d{1,2})\.(\d{1,3})$/, // HH:MM:SS.FF (legacy format)
      /^(\d{1,2}):(\d{1,2}):(\d{1,2})$/, // HH:MM:SS
      /^(\d{1,2}):(\d{1,2})$/, // MM:SS
      /^(\d+\.?\d*)$/, // Plain seconds
    ];

    // Try HH:MM:SS:FF format (Final Cut Pro)
    let match = timeStr.match(timeRegexes[0]);
    if (match) {
      const [, hours, minutes, seconds, frames] = match;
      const totalFrames =
        parseInt(hours) * 3600 * xmlFps +
        parseInt(minutes) * 60 * xmlFps +
        parseInt(seconds) * xmlFps +
        parseInt(frames);
      const timeInSeconds = totalFrames / xmlFps;

      return timeInSeconds;
    }

    // Try HH:MM:SS.FF format (legacy)
    match = timeStr.match(timeRegexes[1]);
    if (match) {
      const [, hours, minutes, seconds, frames] = match;
      const totalFrames =
        parseInt(hours) * 3600 * xmlFps +
        parseInt(minutes) * 60 * xmlFps +
        parseInt(seconds) * xmlFps +
        parseInt(frames);
      return totalFrames / xmlFps;
    }

    // Try HH:MM:SS format
    match = timeStr.match(timeRegexes[2]);
    if (match) {
      const [, hours, minutes, seconds] = match;
      return (
        parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds)
      );
    }

    // Try MM:SS format
    match = timeStr.match(timeRegexes[3]);
    if (match) {
      const [, minutes, seconds] = match;
      return parseInt(minutes) * 60 + parseInt(seconds);
    }

    // Try plain seconds
    match = timeStr.match(timeRegexes[4]);
    if (match) {
      return parseFloat(match[1]);
    }

    return null;
  };

  const formatTimeWithFrames = (
    timeInSeconds: number,
    fps: number = 30
  ): string => {
    // For fractional FPS, we need to handle frame calculations differently
    const totalFrames = Math.round(timeInSeconds * fps);

    // Calculate seconds and frames properly for fractional FPS
    const totalSecondsInt = Math.floor(totalFrames / fps);
    const remainingFrames = Math.round(totalFrames - totalSecondsInt * fps);

    const hours = Math.floor(totalSecondsInt / 3600);
    const minutes = Math.floor((totalSecondsInt % 3600) / 60);
    const seconds = totalSecondsInt % 60;

    const formatted =
      `${hours.toString().padStart(2, "0")}:` +
      `${minutes.toString().padStart(2, "0")}:` +
      `${seconds.toString().padStart(2, "0")}:` +
      `${remainingFrames.toString().padStart(2, "0")}`;

    return formatted;
  };

  const applyCutPoints = async (
    cutPoints: Array<{ timeInSeconds: number; originalFrame: number }>,
    xmlFps: number = 30,
    signal?: AbortSignal
  ) => {
    try {
      // Early return if no valid cut points
      if (!cutPoints || cutPoints.length === 0) {
        return;
      }

      // Convert each cut point to HH:MM:SS format and create segment data
      const allDataTimeSegments: any[] = [];
      const segmentAnalytics: Record<string, any> = {};

      // Process in batches to prevent blocking
      const batchSize = 50;
      for (let i = 0; i < cutPoints.length; i += batchSize) {
        if (signal?.aborted)
          throw new DOMException("Operation aborted", "AbortError");

        const batch = cutPoints.slice(i, i + batchSize);
        batch.forEach((cutPoint) => {
          const formattedTime = formatTimeWithFrames(
            cutPoint.timeInSeconds,
            xmlFps
          );

          // Parse the formatted time to create segment data structure
          const parsed = parseTimeInput(formattedTime, xmlFps);
          if (parsed && parsed.timeInSeconds > 0) {
            allDataTimeSegments.push({
              input: formattedTime,
              hours: parsed.dataTimeSegment.hours,
              minutes: parsed.dataTimeSegment.minutes,
              seconds: parsed.dataTimeSegment.seconds,
              frames: parsed.dataTimeSegment.frames,
              fps: xmlFps,
            });
          }
        });

        // Yield control periodically
        await new Promise((resolve) => setTimeout(resolve, 0));
      }

      // Create segment analytics similar to AutoCutPopup
      const sortedCutTimes = [...cutPoints].sort(
        (a, b) => a.timeInSeconds - b.timeInSeconds
      );

      // Since we're only using start times, use all cut points for interior cuts
      const filteredCutTimes = sortedCutTimes;

      // First segment: always starts at 00:00:00:00 to first cut (if any cuts exist)
      if (
        filteredCutTimes.length > 0 &&
        filteredCutTimes[0].timeInSeconds > 0
      ) {
        segmentAnalytics[`segment1`] = {
          segmentId: 1,
          segmentName: `Segment 1`,
          startTime: "00:00:00:00", // Always start at beginning
          endTime: formatTimeWithFrames(
            filteredCutTimes[0].timeInSeconds,
            xmlFps
          ),
        };
      }

      // Middle segments: from each cut to the next cut
      for (let i = 0; i < filteredCutTimes.length - 1; i++) {
        const startTime = filteredCutTimes[i].timeInSeconds;
        const endTime = filteredCutTimes[i + 1].timeInSeconds;

        // Only create segment if it has positive duration
        if (endTime > startTime) {
          const segmentId = i + 2;
          segmentAnalytics[`segment${segmentId}`] = {
            segmentId,
            segmentName: `Segment ${segmentId}`,
            startTime: formatTimeWithFrames(startTime, xmlFps),
            endTime: formatTimeWithFrames(endTime, xmlFps),
          };
        }
      }

      // Final segment: from last cut to end of video
      // Note: We'll create this without end time since we don't have access to video duration here
      // The end time can be updated later when the video context is available
      if (filteredCutTimes.length > 0) {
        const finalSegmentId = filteredCutTimes.length + 1;
        segmentAnalytics[`segment${finalSegmentId}`] = {
          segmentId: finalSegmentId,
          segmentName: `Segment ${finalSegmentId}`,
          startTime: formatTimeWithFrames(
            filteredCutTimes[filteredCutTimes.length - 1].timeInSeconds,
            xmlFps
          ),
          endTime: formatTimeWithFrames(
            99 * 3600 + 59 * 60 + 59 + 29 / xmlFps,
            xmlFps
          ), // Placeholder in correct format
        };
      }

      if (
        Object.keys(segmentAnalytics).length !==
        filteredCutTimes.length + 1
      ) {
      }

      // Store segments in segment store - only if we have valid data
      if (allDataTimeSegments && allDataTimeSegments.length > 0) {
        setTimeFrameSegment(allDataTimeSegments);
      } else {
        console.log("No valid segments to store");
      }

      // Trigger cuts by dispatching events that the editor can listen to
      // Use requestAnimationFrame for better performance
      const dispatchCuts = async () => {
        for (let index = 0; index < filteredCutTimes.length; index++) {
          if (signal?.aborted) break;

          const cutPoint = filteredCutTimes[index];
          const timeInSeconds = cutPoint.timeInSeconds;
          const framePosition = cutPoint.originalFrame; // Use original frame number directly

          const event = new CustomEvent("xml-split-overlay", {
            detail: {
              timeInSeconds,
              framePosition,
              index,
              total: filteredCutTimes.length,
              xmlFps,
              cutPoints: filteredCutTimes.map((cp) => cp.timeInSeconds), // Convert back for backward compatibility
            },
          });

          window.dispatchEvent(event);

          // Use requestAnimationFrame for better performance
          await new Promise((resolve) =>
            requestAnimationFrame(() => resolve(undefined))
          );
          // Small delay to prevent overwhelming the system
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      };

      await dispatchCuts();

      // Dispatch completion event
      if (!signal?.aborted) {
        // Store cut points in cutStore AFTER successful splits
        // Only store if we have valid cut points
        if (allDataTimeSegments && allDataTimeSegments.length > 0) {
          // Merge with existing data, overwriting duplicates based on timestamp
          const existingCuts = cutTimes.filter(
            (existing) =>
              !allDataTimeSegments.some(
                (newCut) => newCut.input === existing.input
              )
          );
          const mergedCutTimes = [...existingCuts, ...allDataTimeSegments];
          setCutTimes(mergedCutTimes);
        }

        const event = new CustomEvent("xml-splits-completed", {
          detail: {
            totalCuts: filteredCutTimes.length,
            cutPoints: filteredCutTimes.map((cp) => cp.timeInSeconds), // Convert back for backward compatibility
            segments: Object.keys(segmentAnalytics).length,
            xmlFps,
            originalCutCount: sortedCutTimes.length,
            filteredCutCount: filteredCutTimes.length,
          },
        });
        window.dispatchEvent(event);

        // Clear FPS override after completion (optional - you can keep it active)
        // setXmlFpsOverride(null);
      }
    } catch (error) {
      console.error("❌ Error applying cut points:", error);
    }
  };

  const parseTimeInput = (
    input: string,
    fps: number = 30
  ): { timeInSeconds: number; dataTimeSegment: any } | null => {
    // Support both Final Cut Pro format (HH:MM:SS:FF) and legacy format (HH:MM:SS.FF)
    const fcpTimeRegex = /^(\d{1,2}):(\d{1,2}):(\d{1,2}):(\d{1,3})$/; // HH:MM:SS:FF
    const legacyTimeRegex = /^(\d{1,2}):(\d{1,2}):(\d{1,2})\.(\d{1,3})$/; // HH:MM:SS.FF

    let match = input.match(fcpTimeRegex) || input.match(legacyTimeRegex);

    if (match) {
      const [, hours, minutes, seconds, frames] = match;
      const frameNumber = parseInt(frames);

      if (frameNumber >= fps) {
        return null;
      }

      const totalFrames =
        parseInt(hours) * 3600 * fps +
        parseInt(minutes) * 60 * fps +
        parseInt(seconds) * fps +
        frameNumber;

      const timeInSeconds = totalFrames / fps;

      const dataTimeSegment = {
        input,
        hours: parseInt(hours),
        minutes: parseInt(minutes),
        seconds: parseInt(seconds),
        frames: frameNumber,
        fps: fps,
      };

      return { timeInSeconds, dataTimeSegment };
    }

    return null;
  };
  return (
    <>
      {/* Custom warning modal for no cut points */}
      <CustomModal
        open={showNoCutPointsWarning}
        onClose={() => setShowNoCutPointsWarning(false)}
        title="Warning"
      >
        <div className="text-white text-sm mb-6">
          No valid cut points found in the XML file. Please check the file
          format and try again.
        </div>
        <div className="flex justify-end">
          <Button
            onClick={() => setShowNoCutPointsWarning(false)}
            customClass="text-white font-inter px-6"
            variant="danger"
          >
            OK
          </Button>
        </div>
      </CustomModal>

      {/* Main import modal */}
      <CustomModal
        open={modalImportMedia}
        onClose={() => {
          handleCancel();
        }}
        title="Import Markers"
      >
        {isProcessing ? (
          // Processing state - replace all content
          <div className="flex flex-col items-center justify-center py-8">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span className="text-white text-lg font-medium">
                Processing split...
              </span>
            </div>
            <Button
              onClick={handleCancel}
              customClass={"text-white font-inter px-6"}
              variant="danger"
            >
              Cancel
            </Button>
          </div>
        ) : (
          // Normal import form
          <FormControl>
            <RadioGroup
              className="gap-4"
              name="col-radio-buttons-group"
              value={importType}
              onChange={(e) => setImportType(e.target.value)}
            >
              <FormControlLabel
                value="fcpxml"
                control={
                  <Radio
                    icon={<UncheckedIcon />}
                    checkedIcon={<CheckedIcon />}
                  />
                }
                label="FCPX XML"
                sx={{
                  "& .MuiFormControlLabel-label": {
                    fontSize: "14px",
                    fontFamily: "Inter, sans-serif",
                    color: "white",
                  },
                }}
              />
              <FormControlLabel
                value="edl"
                control={
                  <Radio
                    icon={<UncheckedIcon />}
                    checkedIcon={<CheckedIcon />}
                  />
                }
                label="EDL"
                sx={{
                  "& .MuiFormControlLabel-label": {
                    fontSize: "14px",
                    fontFamily: "Inter, sans-serif",
                    color: "white",
                  },
                }}
              />

              <FormControlLabel
                value="xml"
                control={
                  <Radio
                    icon={<UncheckedIcon />}
                    checkedIcon={<CheckedIcon />}
                  />
                }
                label="XML (FCP7XML) "
                sx={{
                  "& .MuiFormControlLabel-label": {
                    fontSize: "14px",
                    fontFamily: "Inter, sans-serif",
                    color: "white",
                  },
                }}
              />
            </RadioGroup>

            {/* Hidden file input for XML/FCPXML/EDL import */}
            <input
              ref={fileInputRef}
              type="file"
              accept={getAcceptAttribute()}
              onChange={handleFileSelect}
              className="hidden"
            />

            <div className="flex gap-4 mt-8 items-center w-full">
              <Button
                onClick={handleCancel}
                customClass={"flex-grow shrink-0 text-white font-inter"}
                disabled={false}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleImportMedia()}
                customClass={"flex-grow shrink-0 text-white font-inter"}
                variant="danger"
              >
                Import
              </Button>
            </div>
          </FormControl>
        )}
      </CustomModal>
    </>
  );
};

export default ImportMarkersModal;
