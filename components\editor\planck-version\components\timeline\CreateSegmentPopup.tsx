import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { useSegmentStore } from "@/store/segmentStore";
import { useTimeline } from "../../contexts/timeline-context";
import { useAppContext } from "../../hooks/useAppContext";
import { useCutStore } from "@/store/cutStore";
interface CreateSegmentPopupProps {
  onCreateSegment: (timeInSeconds: number) => void;
  currentFrame: number;
  fps: number;
  formatTime: (frames: number) => string;
}

export const CreateSegmentPopup: React.FC<CreateSegmentPopupProps> = ({
  onCreateSegment,
  currentFrame,
  fps,
  formatTime,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [timeInputs, setTimeInputs] = useState<string[]>([""]);
  const [errors, setErrors] = useState<string[]>([""]);
  const { isLoading: isGenerating } = useAppContext();
  const setTimeFrameSegment = useSegmentStore(
    (state) => state.setTimeFrameSegment
  );
  const { isProjectLoading } = useTimeline();
  const { addCutTime } = useCutStore();
  // Parse time input in format HH:MM:SS.FF (hours:minutes:seconds.frames)
  const parseTimeInput = (
    input: string
  ): { timeInSeconds: number; dataTimeSegment: any } | null => {
    const timeRegex = /^(\d{1,2}):(\d{1,2}):(\d{1,2})\.(\d{1,3})$/;
    const match = input.match(timeRegex);

    if (!match) {
      return null;
    }

    const [, hours, minutes, seconds, frames] = match;

    // Validate frame number against actual FPS
    const frameNumber = parseInt(frames);
    if (frameNumber >= fps) {
      return null; // Invalid frame number for this FPS
    }

    // Convert display frames to actual frame position
    // The display format shows frames based on the actual FPS
    const displayFrames = parseInt(frames);
    const totalFrames =
      parseInt(hours) * 3600 * fps +
      parseInt(minutes) * 60 * fps +
      parseInt(seconds) * fps +
      displayFrames; // Use display frames directly as they match the actual FPS

    const timeInSeconds = totalFrames / fps;

    const dataTimeSegment = {
      input,
      hours: parseInt(hours),
      minutes: parseInt(minutes),
      seconds: parseInt(seconds),
      frames: parseInt(frames),
      fps: fps,
    };

    return { timeInSeconds, dataTimeSegment };
  };

  const handleCreateSegment = () => {
    const newErrors = [...errors];
    let hasError = false;
    const validTimes: number[] = [];
    const allDataTimeSegments: any[] = []; // Array to collect all dataTimeSegment objects

    timeInputs.forEach((input, idx) => {
      if (!input.trim()) {
        newErrors[idx] = "Please enter a time value";
        hasError = true;
        return;
      }

      const result = parseTimeInput(input);
      if (result === null) {
        newErrors[
          idx
        ] = `Invalid time format. Use HH:MM:SS.FF where frames are 0-${
          fps - 1
        } (e.g., 00:00:02.${Math.floor(fps / 4)
          .toString()
          .padStart(2, "0")})`;
        hasError = true;
        return;
      }

      if (result.timeInSeconds < 0) {
        newErrors[idx] = "Time cannot be negative";
        hasError = true;
        return;
      }

      newErrors[idx] = "";
      validTimes.push(result.timeInSeconds);
      allDataTimeSegments.push(result.dataTimeSegment); // Push the dataTimeSegment object, not timeInSeconds
    });

    setErrors(newErrors);
    if (hasError) return;

    // Save all dataTimeSegment objects
    setTimeFrameSegment(allDataTimeSegments);

    useCutStore.setState((state) => ({
      cutTimes: [...state.cutTimes, ...allDataTimeSegments],
    }));

    const sortedTimes = [...validTimes].sort((a, b) => b - a);

    sortedTimes.forEach((time, index) => {
      const segmentId = index + 1;
      const segmentName = `Segment ${segmentId}`;
      const startTime = time;
      const endTime = sortedTimes[index - 1] || time + 2; // 2 detik fallback

      const metadata = {
        segmentId,
        segmentName,
        startTime,
        endTime,
        timeFrameSegment: allDataTimeSegments,
        audioFile: "",
        characterSize: "medium",
        weather: "dry",
        environment: "indoor",
        groundTexture: "soft",
        groundMaterial: "carpet",
        footwear: "barefoot",
        fullPrompt: "",
        negativePrompt:
          "talking, speech, voice, dialogue, conversation, ambient noise, background noise, room tone, breathing, fabric rustle, music, chatter, murmuring, whispering, echo",
        seed: Math.floor(Math.random() * 100),
        qualitySounds: 60,
        guidenceStrength: 23,
      };
    });

    // Sort validTimes in descending order and call onCreateSegment for each
    [...validTimes].sort((a, b) => b - a).forEach(onCreateSegment);
    setTimeInputs([""]);
    setErrors([""]);
    setIsOpen(false);
  };

  const handleInputChange = (
    idx: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newInputs = [...timeInputs];
    newInputs[idx] = e.target.value;
    setTimeInputs(newInputs);
    const newErrors = [...errors];
    newErrors[idx] = "";
    setErrors(newErrors);
  };

  const handleAddInput = () => {
    setTimeInputs([...timeInputs, ""]);
    setErrors([...errors, ""]);
  };

  const handleRemoveInput = (idx: number) => {
    if (timeInputs.length === 1) return;
    const newInputs = timeInputs.filter((_, i) => i !== idx);
    const newErrors = errors.filter((_, i) => i !== idx);
    setTimeInputs(newInputs);
    setErrors(newErrors);
  };

  // (moved to handleInputChange above, now takes index)

  const handleUseCurrentTime = (idx: number) => {
    const currentTime = formatTime(currentFrame);
    const newInputs = [...timeInputs];
    newInputs[idx] = currentTime;
    setTimeInputs(newInputs);
    const newErrors = [...errors];
    newErrors[idx] = "";
    setErrors(newErrors);
  };

  const handleKeyDown = (e: React.KeyboardEvent, idx: number) => {
    if (e.key === "Enter") {
      handleCreateSegment();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`btn-create-segment h-7 px-3 text-xs font-medium bg-yellow-400 hover:bg-yellow-500 text-black border-yellow-500 hover:border-yellow-600 ${
            isGenerating && "pointer-events-none opacity-50"
          }`}
        >
          <Plus className="h-3 w-3 mr-1" />
          Create Segment
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Segment</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Split Times (HH:MM:SS.FF)</Label>
            {timeInputs.map((input, idx) => (
              <div className="flex items-center gap-2 mb-2" key={idx}>
                <Input
                  placeholder={`00:00:02.${Math.floor(fps / 4)
                    .toString()
                    .padStart(2, "0")}`}
                  value={input}
                  onChange={(e) => handleInputChange(idx, e)}
                  onKeyDown={(e) => handleKeyDown(e, idx)}
                  className="font-mono flex-1"
                  autoFocus={idx === 0}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.preventDefault();
                    handleUseCurrentTime(idx);
                  }}
                  title="Use current time"
                >
                  <span role="img" aria-label="clock">
                    🕒
                  </span>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.preventDefault();
                    handleRemoveInput(idx);
                  }}
                  disabled={timeInputs.length === 1}
                  title="Remove"
                >
                  <span role="img" aria-label="remove">
                    ✖️
                  </span>
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddInput}
              className="mb-2"
            >
              <Plus className="h-3 w-3 mr-1" /> Add Time
            </Button>
            {errors.some((e) => e) &&
              errors.map(
                (err, idx) =>
                  err && (
                    <p key={idx} className="text-sm text-red-500">
                      Input {idx + 1}: {err}
                    </p>
                  )
              )}
            <p className="text-xs text-gray-500">
              Enter one or more times to split the segment (Current video: {fps}{" "}
              FPS, frames: 0-{fps - 1})
            </p>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUseCurrentTime(0)}
              className="flex-1"
            >
              Use Current Time
            </Button>
          </div>

          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1 btn-close-popup"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateSegment}
              className="flex-1 bg-yellow-400 hover:bg-yellow-500 text-black "
            >
              Create Segment
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
