const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");

console.log("✅ preload.js loaded");

contextBridge.exposeInMainWorld("electronAPI", {
  onClearProject: (callback) => {
    ipcRenderer.on("clear-project", (event, ...args) => {
      callback(event, ...args);
    });
  },
  // File system operations
  readFile: (filePath) => ipcRenderer.invoke("read-file-buffer", filePath),
  writeFile: (filePath, data) =>
    ipcRenderer.invoke("write-file", filePath, data),
  fileExists: (filePath) => ipcRenderer.invoke("file-exists", filePath),
  moveFile: (sourcePath, destinationPath) =>
    ipcRenderer.invoke("move-file", sourcePath, destinationPath),

  // Audio-specific operations
  readAudioFile: (filePath) => ipcRenderer.invoke("read-audio-file", filePath),
  checkFileExists: (filePath) =>
    ipc<PERSON>enderer.invoke("check-file-exists", filePath),
  validateAudioFile: (filePath) =>
    ipcRenderer.invoke("validate-audio-file", filePath),
  findAudioFiles: (searchPaths, audioFileNames) =>
    ipcRenderer.invoke("find-audio-files", searchPaths, audioFileNames),
  listDirectoryFiles: (dirPath, extensions) =>
    ipcRenderer.invoke("list-directory-files", dirPath, extensions),

  // Directory operations
  readDirectory: (dirPath, options) =>
    ipcRenderer.invoke("read-directory", dirPath, options),
  
  onSaveVideo: (buffer, metadata, aiVideoAnalytics) =>
    ipcRenderer.invoke("save-video", buffer, metadata, aiVideoAnalytics),

  onSaveAsVideo: (buffer, metadata, aiVideoAnalytics) =>
    ipcRenderer.invoke("save-as-video", buffer, metadata, aiVideoAnalytics),

  getLastSavedMetadata: () => ipcRenderer.invoke("get-last-saved-metadata"),

  openProject: () => ipcRenderer.invoke("open-project"),

  clearProjectMetadata: () => ipcRenderer.invoke("clear-project-metadata"),
  resetProjectState: (newState = null) =>
    ipcRenderer.invoke("reset-project-state", newState),
  getLastSavedMetadata: () => ipcRenderer.invoke("get-last-saved-metadata"),

  minimizeWindow: () => ipcRenderer.send("minimize-window"),
  maximize: () => ipcRenderer.send("maximize-window"),
  closeWindow: () => ipcRenderer.send("close-window"),

  // python
  runPythonAnalysis: (payload) =>
    ipcRenderer.invoke("python:run-analysis", payload),
  runPythonDescription: (payload) =>
    ipcRenderer.invoke("python:description", payload),
  checkPythonServer: () => ipcRenderer.invoke("python:check-server"),
  startPythonServer: () => ipcRenderer.invoke("python:start-server"),
  stopPythonServer: () => ipcRenderer.invoke("python:stop-server"),
  restartPythonServer: () => ipcRenderer.invoke("python:restart-server"),
  getPythonServerInfo: () => ipcRenderer.invoke("python:server-info"),

  // Real-time Python analysis progress listener
  onPythonAnalysisProgress: (callback) => {
    ipcRenderer.on("python-analysis-progress", (event, progressData) => {
      callback(progressData);
    });
  },

  // Remove Python analysis progress listener
  removePythonAnalysisProgressListener: () => {
    ipcRenderer.removeAllListeners("python-analysis-progress");
  },

  // File system operations - ADD THESE MISSING METHODS
  readFileBuffer: (path) => ipcRenderer.invoke("read-file-buffer", path),
  writeFile: (path, data) => ipcRenderer.invoke("write-file", path, data),
  fileExists: (path) => ipcRenderer.invoke("file-exists", path),
  readDirectory: (path, options) =>
    ipcRenderer.invoke("read-directory", path, options),

  // Path utilities
  path: {
    join: (...paths) => require("path").join(...paths),
    basename: (filepath, ext) => require("path").basename(filepath, ext),
    dirname: (filepath) => require("path").dirname(filepath),
    extname: (filepath) => require("path").extname(filepath),
    resolve: (...paths) => require("path").resolve(...paths),
  },

  moveFile: (sourcePath, destinationPath) =>
    ipcRenderer.invoke("move-file", sourcePath, destinationPath),
  createDirectory: (path) => ipcRenderer.invoke("create-directory", path),
  
  // Dialog operations
  showSaveDialog: (options) => ipcRenderer.invoke("show-save-dialog", options),
  
  // WAV to AAF conversion
  convertWavToAAF: (wavPath) => ipcRenderer.invoke("convert-wav-to-aaf", wavPath),
  
  // Video and Audio combination
  combineVideoAudio: (videoPath, audioSegments, outputPath, useVideoAudio, videoTrimming) => 
    ipcRenderer.invoke("combine-video-audio", videoPath, audioSegments, outputPath, useVideoAudio, videoTrimming),
  
  // File deletion
  deleteFile: (filePath) => ipcRenderer.invoke("delete-file", filePath),
  
  // File buffer writing
  writeFileBuffer: (filePath, buffer) => ipcRenderer.invoke("write-file-buffer", filePath, buffer),
  
  // Window title operations
  updateWindowTitle: (projectTitle) => ipcRenderer.invoke("update-window-title", projectTitle),
  getWindowTitle: () => ipcRenderer.invoke("get-window-title"),
  
  // Save menu items state
  updateSaveMenuItems: (enabled) => ipcRenderer.invoke("update-save-menu-items", enabled),
  
  // Menu event listeners
  onMenuNewProject: (callback) => ipcRenderer.on("menu-new-project", callback),
  onMenuOpenProject: (callback) => ipcRenderer.on("menu-open-project", callback),
  onMenuSave: (callback) => ipcRenderer.on("menu-save", callback),
  onMenuSaveAs: (callback) => ipcRenderer.on("menu-save-as", callback),
  onMenuImportMedia: (callback) => ipcRenderer.on("menu-import-media", callback),
  onMenuImport: (callback) => ipcRenderer.on("menu-import", callback),
  onMenuExportMarkers: (callback) => ipcRenderer.on("menu-export-markers", callback),
  onMenuExport: (callback) => ipcRenderer.on("menu-export", callback),
  onMenuAddCutMarker: (callback) => ipcRenderer.on("menu-add-cut-marker", callback),
  onMenuAddManualCutMarker: (callback) => ipcRenderer.on("menu-add-manual-cut-marker", callback),
  onMenuSelectCutMarker: (callback) => ipcRenderer.on("menu-select-cut-marker", callback),
  onMenuAnalytics: (callback) => {
    ipcRenderer.on("menu-analytics", (event, ...args) => {
      callback(event, ...args);
    });
  },
  
  // Remove menu event listeners
  removeAllMenuListeners: () => {
    ipcRenderer.removeAllListeners("menu-new-project");
    ipcRenderer.removeAllListeners("menu-open-project");
    ipcRenderer.removeAllListeners("menu-save");
    ipcRenderer.removeAllListeners("menu-save-as");
    ipcRenderer.removeAllListeners("menu-import-media");
    ipcRenderer.removeAllListeners("menu-import");
    ipcRenderer.removeAllListeners("menu-export-markers");
    ipcRenderer.removeAllListeners("menu-export");
    ipcRenderer.removeAllListeners("menu-add-cut-marker");
    ipcRenderer.removeAllListeners("menu-add-manual-cut-marker");
    ipcRenderer.removeAllListeners("menu-select-cut-marker");
    ipcRenderer.removeAllListeners("menu-analytics");
  },
  
  // Auto cut functionality
  selectVideoFile: () => ipcRenderer.invoke("select-video-file"),
  videoAutoCut: (config) => ipcRenderer.invoke("video-auto-cut", config),
});
