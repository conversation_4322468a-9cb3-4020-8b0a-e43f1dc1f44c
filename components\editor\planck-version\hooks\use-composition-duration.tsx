import { useMemo } from "react";
import { Overlay } from "../types";
import { FPS } from "../constants";
import { getEffectiveFPS } from "../utils/fps-utils";

export const useCompositionDuration = (overlays: Overlay[]) => {
  // Get the effective FPS from overlays
  const effectiveFPS = overlays.length > 0 ? getEffectiveFPS(overlays) : FPS;

  // Calculate the total duration in frames based on overlays
  const durationInFrames = useMemo(() => {
    if (!overlays.length) return effectiveFPS * 5; // Default to 5 seconds when empty

    const maxEndFrame = overlays.reduce((maxEnd, overlay) => {
      const endFrame = overlay.from + overlay.durationInFrames;
      return Math.max(maxEnd, endFrame);
    }, 0);

    // Ensure we always have at least 1 second duration
    return Math.max(maxEndFrame, effectiveFPS);
  }, [overlays, effectiveFPS]);

  return {
    durationInFrames,
    durationInSeconds: durationInFrames / effectiveFPS,
    getDurationInSeconds: () => durationInFrames / effectiveFPS,
    getDurationInFrames: () => durationInFrames,
    fps: effectiveFPS,
  };
};
