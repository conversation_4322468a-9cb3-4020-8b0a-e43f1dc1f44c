
import os
import sys
import tempfile

# Add mmaudio to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
mmaudio_path = os.path.join(script_dir, 'mmaudio')
if mmaudio_path not in sys.path:
    sys.path.insert(0, mmaudio_path)

import logging
import json
import time
import re
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List

import cv2
import numpy as np
import torch
import torchaudio
import requests
import base64
import traceback
import uuid
import time

# Import MMAudio modules
from mmaudio.eval_utils import ModelConfig, all_model_cfg, generate, load_video, make_video, setup_eval_logging
from mmaudio.model.flow_matching import FlowMatching
from mmaudio.model.networks import MMAudio, get_my_mmaudio
from mmaudio.model.utils.features_utils import FeaturesUtils

#import post processing
import librosa
import numpy as np
from post_processing.lib import spec_utils
from post_processing.lib import nets
from post_processing.inference import Separator
import subprocess
import shutil

# Setup logging
setup_eval_logging()
log = logging.getLogger(__name__)

# Azure OpenAI API configuration
AZURE_OPENAI_ENDPOINT = "https://mochachai-openai.openai.azure.com/"
AZURE_OPENAI_API_KEY = "CQJvNF9sVJoZNXF13wsB1QjeuxtDJqx9XXidlNMoMZKlVl6mm93JJQQJ99BDACYeBjFXJ3w3AAABACOGEnof"
AZURE_OPENAI_API_VERSION = "2024-02-15-preview"
AZURE_OPENAI_MODEL = "gpt-4o-mini"

POST_PROCESSOR__SR = 44100
POST_PROCESSOR__N_FFT = 2048
POST_PROCESSOR__HOP_LENGTH = 1024
POST_PROCESSOR__BATCHSIZE = 4
POST_PROCESSOR__CROPSIZE = 256
POST_PROCESSOR__DEVICE = torch.device('cpu')  # Use CPU by default
POST_PROCESSOR__MODEL_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)),'post_processing' ,'models', 'baseline.pth')

DESCRIPTIONS_TYPES = {
            "success": True,
            "message": "Retrieved all the valid values",
            "data": {
                "character_size": ["small", "medium", "large"],
                "weather": ["dry", "wet"],
                "environment": [
                    {
                        "name": "indoor",
                        "subtypes": [
                            {
                                "name": "soft",
                                "subtypes": ["carpet", "rubber"]
                            },
                            {
                                "name": "hard",
                                "subtypes": ["concrete", "tile", "wood", "metal"]
                            }
                        ]
                    },
                    {
                        "name": "outdoor",
                        "subtypes": [
                            {
                                "name": "soft",
                                "subtypes": ["grass", "soil", "sand", "rubber", "gravel"]
                            },
                            {
                                "name": "hard",
                                "subtypes": ["asphalt", "concrete/tile", "stones/pebbles", "metal"]
                            }
                        ]
                    }
                ],
                "footwear": ["barefoot", "hard sole shoes", "sneakers", "flip flops/slippers",
                            "sandals", "high heels", "clogs"]
            }
        }

def initialize_model(model='large_44k_v2',device='cpu', dtype=torch.float32):
    """Initialize the MMAudio model and its components"""
    print("Loading model...")
    
    # Use large_44k_v2 model configuration
    model: ModelConfig = all_model_cfg[model]
    model.download_if_needed()
    
    # Get sequence configuration
    seq_cfg = model.seq_cfg

    # Load the model
    net: MMAudio = get_my_mmaudio(model.model_name).to(device, dtype).eval()
    net.load_weights(torch.load(model.model_path, map_location=device, weights_only=True))
    log.info(f'Loaded model weights from {model.model_path}')

    # Initialize feature utils
    feature_utils = FeaturesUtils(
        tod_vae_ckpt=model.vae_path,
        synchformer_ckpt=model.synchformer_ckpt,
        enable_conditions=True,
        mode=model.mode,
        bigvgan_vocoder_ckpt=model.bigvgan_16k_path,
        need_vae_encoder=False
    )
    feature_utils = feature_utils.to(device, dtype).eval()

    print("Model loaded successfully!")
    return net, feature_utils, seq_cfg

def initialize_post_processor():
    """Initialize the post-processor"""
    # Try with is_complex=True first
    model = nets.CascadedNet(POST_PROCESSOR__N_FFT, POST_PROCESSOR__HOP_LENGTH, 32, 128)
    state_dict = torch.load(POST_PROCESSOR__MODEL_PATH, map_location='cpu')
    model.load_state_dict(state_dict, strict=False)
    model.to(POST_PROCESSOR__DEVICE)
    print("Post-processor loaded successfully")
    return model

def librosa_post_process_audio(audio_tensor, sample_rate=44100):
    """
    Post-process audio by saving it temporarily, reloading with librosa, and processing
    
    Args:
        audio_tensor: PyTorch tensor containing audio data from mmaudio output
        sample_rate: Sample rate for audio processing
    
    Returns:
        Processed audio as PyTorch tensor
    """
    import tempfile
    
    # Convert tensor to numpy if needed
    if isinstance(audio_tensor, torch.Tensor):
        audio_np = audio_tensor.cpu().numpy()
    else:
        audio_np = audio_tensor
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        # Save audio tensor to temporary file
        if audio_np.ndim == 1:
            # Mono audio - convert to stereo for consistency
            audio_stereo = np.asarray([audio_np, audio_np])
        else:
            audio_stereo = audio_np
            
        # Save as wav file using torchaudio
        audio_tensor_for_save = torch.from_numpy(audio_stereo).float()
        torchaudio.save(temp_path, audio_tensor_for_save, sample_rate)
        
        # Load with librosa
        X, sr = librosa.load(
            temp_path, sr=sample_rate, mono=False, dtype=np.float32, res_type='kaiser_fast'
        )
        
        # Handle mono files (convert to stereo)
        if X.ndim == 1:
            X = np.asarray([X, X])
        
        # Convert back to tensor
        processed_audio = torch.from_numpy(X).float()
        
        return processed_audio
        
    finally:
        # Clean up temporary file
        if os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except Exception as e:
                log.warning(f"Failed to remove temporary file {temp_path}: {e}")

def cut_video_segment(video_path, start_time, end_time, output_path=None):
    """
    Cut a video segment using ffmpeg and return the temporary file path
    
    Args:
        video_path (str): Path to the input video
        start_time (float): Start time in seconds
        end_time (float): End time in seconds  
        output_path (str, optional): Output path. If None, creates temporary file
        
    Returns:
        str: Path to the cut video segment
    """
    try:
        if output_path is None:
            # Create temporary file in same directory as input video
            video_dir = os.path.dirname(os.path.abspath(video_path))
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            temp_name = f"{video_name}__temp_{uuid.uuid4().hex}.mp4"
            output_path = os.path.join(video_dir, temp_name)
        
        # Use ffmpeg to cut the video segment
        cmd = [
            'ffmpeg',
            '-ss', str(start_time),
            '-i', str(video_path),
            '-t', str(end_time - start_time),
            # '-c', 'copy',  
            '-avoid_negative_ts', 'make_zero',
            '-y',  # Overwrite output file
            str(output_path)
        ]
        
        log.info(f'Cutting video segment: {start_time}s to {end_time}s')
        log.info(f'FFmpeg command: {" ".join(cmd)}')
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
       
        if os.path.exists(output_path):
            log.info(f'Video segment created successfully: {output_path}')
            return output_path
        else:
            log.error('Video segment file was not created')
            return None
            
    except subprocess.CalledProcessError as e:
        log.error(f'FFmpeg error: {e.stderr}')
        return None
    except Exception as e:
        log.error(f'Error cutting video segment: {e}')
        return None


def analyze_video_content_with_openai(video_path: str) -> Dict[str, Any]:
    """
    Analyze video content by extracting frames and processing with Azure OpenAI
    """
    try:
    
        description_types_json = DESCRIPTIONS_TYPES

        # Extract the data from the structure
        if not isinstance(description_types_json, dict) or 'data' not in description_types_json:
            raise ValueError("Invalid format in description_types.json, expecting 'data' field")

        description_types = description_types_json['data']

        print("==== video_path: ",video_path)
        # Load the video
        video = cv2.VideoCapture(video_path)
        if not video.isOpened():
            raise ValueError("Could not open video file")

        # Get video properties
        total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = video.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0

        # Define how many frames to sample
        num_samples = min(5, total_frames)
        frame_indices = [int(i * total_frames / num_samples) for i in range(num_samples)]

        # Extract frames
        frames = []
        for idx in frame_indices:
            video.set(cv2.CAP_PROP_POS_FRAMES, idx)
            success, frame = video.read()
            if success:
                # Encode frame to base64 for API request
                _, buffer = cv2.imencode('.jpg', frame)
                base64_image = base64.b64encode(buffer).decode('utf-8')
                frames.append(base64_image)

        print("fps: ",fps)
        print("total_frames: ",total_frames)
        print("frame_indices: ",frame_indices)

        video.release()

        # If no frames were extracted, return empty results
        if not frames:
            return {
                "short_description":None,
                "character_size": None,
                "weather": None,
                "environment": None,
                "ground_texture": None,
                "ground_material": None,
                "footwear": None
            }

        # Process frames with Azure OpenAI
        return analyze_frames_with_openai(frames, description_types)

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        log.error(f"Error in analyze_video_content: {str(e)}\nTraceback: {error_traceback}")
        raise

def analyze_frames_with_openai(frames: List[str], description_types: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze video frames with Azure OpenAI API

    Args:
        frames: List of base64-encoded frame images
        description_types: Dictionary of valid parameter values

    Returns:
        Dictionary with analysis results
    """
    # Use up to 5 frames for analysis to get more accurate results
    frames_to_analyze = frames[:5] if len(frames) >= 5 else frames

    # Extract all ground materials from the environment structure
    ground_materials = []
    ground_textures = ["soft", "hard"]

    for env in description_types["environment"]:
        for texture in env["subtypes"]:
            for material in texture["subtypes"]:
                if material not in ground_materials:
                    ground_materials.append(material)

    # Format description types as a string instead of a list for the prompt
    formatted_options = {
        "character_size": ", ".join(description_types["character_size"]),
        "weather": ", ".join(description_types["weather"]),
        "environment": "indoor, outdoor",
        "ground_texture": ", ".join(ground_textures),
        "ground_material": ", ".join(ground_materials),
        "footwear": ", ".join(description_types["footwear"])
    }

    print(f"formatted_options: {formatted_options}")

    # Create prompt for OpenAI
    prompt = f"""
    Analyze these frames from a video and determine the following attributes.
    Generate medium description for the CLIP, it around 50 words.
    Choose ONE value for each attribute from the provided options.
    You MUST choose a value from the given options - do not return null or empty values.
    If uncertain, choose the closest or most likely option based on typical scenarios.

    1. Character Size: {formatted_options["character_size"]}
    2. Weather Condition: {formatted_options["weather"]}
    3. Environment: {formatted_options["environment"]}
    4. Ground Texture: {formatted_options["ground_texture"]}
    5. Ground Material: {formatted_options["ground_material"]}
    6. Footwear: {formatted_options["footwear"]}

    Focus on identifying footstep-related characteristics, especially the ground material and footwear.
    For each attribute, consider what would impact the sound of footsteps in this scene.
    Look at all provided frames to get the most accurate assessment.

    Provide your response in valid JSON format with the following structure:
    {{
        "short_description": "generated_value",
        "character_size": "chosen_value",
        "weather": "chosen_value", 
        "environment": "chosen_value",
        "ground_texture": "chosen_value",
        "ground_material": "chosen_value",
        "footwear": "chosen_value"
    }}
    """
    
    # Prepare API request
    headers = {
        "Content-Type": "application/json",
        "api-key": AZURE_OPENAI_API_KEY
    }

    # Build message content with text and multiple images
    message_content = [
        {
            "type": "text",
            "text": prompt
        }
    ]

    # Add all frames to the message content
    for frame in frames_to_analyze:
        message_content.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{frame}"
            }
        })

    payload = {
        "messages": [
            {
                "role": "system",
                "content": "You are an expert video analyst specialized in audio-visual scene understanding. Your task is to analyze multiple frames from videos and determine characteristics that would affect footstep sounds. Make your best assessment based on all images provided."
            },
            {
                "role": "user",
                "content": message_content
            }
        ],
        "max_tokens": 400,
        "temperature": 0.1
    }

    try:
        # Make request to Azure OpenAI
        response = requests.post(
            f"{AZURE_OPENAI_ENDPOINT}openai/deployments/{AZURE_OPENAI_MODEL}/chat/completions?api-version={AZURE_OPENAI_API_VERSION}",
            headers=headers,
            json=payload
        )

        response.raise_for_status()
        response_data = response.json()

        print(f"response data: {response_data}")
        # Extract JSON from the response text
        result_text = response_data["choices"][0]["message"]["content"]

        print(f"result_text: {result_text}")

        # Find JSON in the response
        import re
        json_match = re.search(r'({[\s\S]*})', result_text)
        if json_match:
            result_json = json.loads(json_match.group(1))
        else:
            # Try to load the whole response as JSON
            try:
                result_json = json.loads(result_text)
            except:
                raise ValueError("Could not parse JSON from OpenAI response")

        # Ensure all required keys are present
        for key in ["short_description","character_size", "weather", "environment", "ground_texture", "ground_material", "footwear"]:
            if key not in result_json:
                result_json[key] = None

        print(f"result_json: {result_json}")

        return result_json

    except Exception as e:
        log.error(f"Error in OpenAI API request: {str(e)}")
        log.error(f"Response status: {response.status_code if 'response' in locals() else 'N/A'}")
        log.error(f"Response content: {response.text if 'response' in locals() else 'N/A'}")
        # Return default empty values if API fails
        return {
            "short_description": None,
            "character_size": None,
            "weather": None,
            "environment": None,
            "ground_texture": None,
            "ground_material": None,
            "footwear": None
        }
    
def mmaudio_inference(data={}): 
    """Run inference on the MMAudio model"""
    
    # Parse input data
    video_path = Path(data.get('video_path')) if data.get('video_path') else None
    prompt = data.get('full_prompt', 'footstep sound only')
    negative_prompt = data.get('negative_prompt', 'talking, speech, voice, dialogue, conversation, ambient noise, background noise, room tone, breathing, fabric rustle, music, chatter, murmuring, whispering, echo')
    duration = round(data.get('duration', 8.0), 2)

    output_name = data.get('output', 'output')
    output_dir = Path(data.get('output_dir', './output/mmaudio'))
    device = data.get('device', 'cpu')

    seed=data.get('seed', 10)
    quality_sounds=data.get('quality_sounds', 25)
    guidance_strength = data.get('guidance_strength', 7.0)
    
    
    # Get models from data
    net = data.get('net', None)
    feature_utils = data.get('feature_utils', None)

    seq_cfg = data.get('seq_cfg', None)
    post_processor_model = data.get('post_processor_model', None)
    selected_segment = data.get('selected_segment', None)
    post_process_strength = data.get('post_process_strength', 0.7)  # 0.0 = no effect, 1.0 = full effect
    skip_post_processing = data.get('skip_post_processing', False)
    
    print("post_process_strength: ",post_process_strength)

    skip_video_composite=data.get('skip_video_composite', False)
    
    # Handle video segment cutting if selectedSegment is provided
    temp_video_path = None
    if video_path and selected_segment:
        start_time = selected_segment.get('start', 0)
        end_time = selected_segment.get('end', duration)
        segment_duration = end_time - start_time
        # Round duration down to 2 decimal places
        duration = round(segment_duration, 2)
        # Get original video duration for comparison
        try:
            import cv2
            cap = cv2.VideoCapture(str(video_path))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            original_duration = frame_count / fps if fps > 0 else duration
            cap.release()
        except:
            original_duration = duration
        
        # Skip cutting if segment duration is very close to original duration (within 0.1 seconds)
        if abs(segment_duration - original_duration) <= 0.1 or segment_duration >= original_duration:
            log.info(f'Segment duration ({segment_duration:.2f}s) is very close to original duration ({original_duration:.2f}s), skipping video cutting')
        else:
            log.info(f'Cutting video segment from {start_time}s to {end_time}s (duration: {segment_duration:.2f}s)')
            temp_video_path = cut_video_segment(str(video_path), start_time, end_time)
            
            if temp_video_path:
                video_path = Path(temp_video_path)
                print("segment_duration: ",segment_duration)
    
                duration = round(segment_duration, 2)
                log.info(f'Using temporary video segment: {video_path}')
            else:
                log.warning('Failed to cut video segment, using original video')

    log.info(f'Using video {video_path}')
    log.info(f'Using input duration: {duration} for {video_path}')
    
    print("OUTPUT: ",seed," ",quality_sounds," ",guidance_strength)
    
    rng = torch.Generator(device=device)
    if seed >= 0:
        rng.manual_seed(seed)
    else:
        rng.seed()
        
    # Ensure duration is set to a reasonable value before loading video
    if not hasattr(seq_cfg, 'duration') or seq_cfg.duration is None:
        seq_cfg.duration = float('inf')
        
    video_info = load_video(video_path, duration_sec=duration)
    clip_frames = video_info.clip_frames
    sync_frames = video_info.sync_frames
    duration = video_info.duration_sec
    
    # Prepare frames
    clip_frames = clip_frames.unsqueeze(0)
    sync_frames = sync_frames.unsqueeze(0)
    
    log.info(f'Using duration: {duration} for {video_path}')
    log.info(f'Video info duration: {video_info.duration_sec}')

    # Use actual video duration from loaded video info to avoid sequence length mismatch
    actual_duration = video_info.duration_sec
    seq_cfg.duration = actual_duration
    log.info(f'Adjusted duration from {duration} to actual video duration: {actual_duration}')
    
    net.update_seq_lengths(seq_cfg.latent_seq_len, seq_cfg.clip_seq_len, seq_cfg.sync_seq_len)

    # Create a new FlowMatching instance with the current num_steps value
    current_fm = FlowMatching(min_sigma=0, inference_mode='euler', num_steps=quality_sounds)

    log.info(f'Prompt: {prompt}')
    log.info(f'Negative prompt: {negative_prompt}')

    with torch.no_grad():
        audios = generate(clip_frames,
                          sync_frames, [prompt],
                          negative_text=[negative_prompt],
                          feature_utils=feature_utils,
                          net=net,
                          fm=current_fm,
                          rng=rng,
                          cfg_strength=guidance_strength)
    
    audio = audios.float().cpu()[0]

    # Convert tensor to numpy for processing
    # audio_np = audio.numpy()
    
    if skip_post_processing:
        # Skip post-processing entirely
        processed_audio = audio
        log.info('Skipping post-processing as requested')
    else:
        print("===== post processing ")
        
        # First apply librosa post-processing (save and reload with librosa)
        librosa_processed_audio = librosa_post_process_audio(audio, seq_cfg.sampling_rate)
        log.info('Applied librosa post-processing (save->reload cycle)')
        
        # Then apply existing post-processing pipeline if model is available
        if post_processor_model is not None:
            # Convert librosa processed audio to numpy for existing pipeline
            librosa_audio_np = librosa_processed_audio.numpy()
            
            # Ensure stereo format
            if librosa_audio_np.ndim == 1:
                librosa_audio_np = np.asarray([librosa_audio_np, librosa_audio_np])
            elif librosa_audio_np.ndim == 2 and librosa_audio_np.shape[0] == 1:
                librosa_audio_np = np.asarray([librosa_audio_np[0], librosa_audio_np[0]])
            
            # Convert to spectrogram
            X_spec = spec_utils.wave_to_spectrogram(librosa_audio_np, POST_PROCESSOR__HOP_LENGTH, POST_PROCESSOR__N_FFT)
            
            # Create separator and process audio
            separator = Separator(
                model=post_processor_model,
                device=POST_PROCESSOR__DEVICE,
                batchsize=POST_PROCESSOR__BATCHSIZE,
                cropsize=POST_PROCESSOR__CROPSIZE,
                postprocess=False
            )
            
            # Apply post processing
            y_spec, _ = separator.separate(X_spec)
            
            # Convert spectrogram back to wave
            processed_audio_np = spec_utils.spectrogram_to_wave(y_spec, hop_length=POST_PROCESSOR__HOP_LENGTH)
            
            # Convert processed audio back to tensor
            processed_audio = torch.from_numpy(processed_audio_np).float()
            log.info('Applied full post-processing pipeline (librosa + existing)')
        else:
            # Use only librosa post-processing if no model available
            processed_audio = librosa_processed_audio
            log.info('Applied librosa post-processing only (no model available)')
        
      
        
    # THEN SAVE AUDIO
    if output_name:
        save_path = output_dir / f'{output_name}.wav'
    else:
        if video_path is not None:
            save_path = output_dir / f'{video_path.stem}.wav'
        else:
            safe_filename = prompt.replace(' ', '_').replace('/', '_').replace('.', '')
            save_path = output_dir / f'{safe_filename}.wav'

    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save the processed audio (ensure correct format)
    if processed_audio.dim() == 1:
        processed_audio = processed_audio.unsqueeze(0)
    torchaudio.save(save_path, processed_audio, seq_cfg.sampling_rate)

    log.info(f'Processed audio saved to {save_path}')
    
    # THEN CREATE VIDEO
    video_save_path = None
    if video_path is not None and not skip_video_composite:
        if output_name:
            video_save_path = output_dir / f'{output_name}.mp4'
        else:
            video_save_path = output_dir / f'{video_path.stem}.mp4'
        video_save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Check if file exists and try to remove it
        if video_save_path.exists():
            try:
                video_save_path.unlink()
                log.info(f'Removed existing file: {video_save_path}')
            except PermissionError:
                log.warning(f'Cannot remove existing file (may be in use): {video_save_path}')
                # Try with a timestamp suffix to avoid conflict
                timestamp = int(time.time() * 1000)
                video_save_path = video_save_path.parent / f'{video_save_path.stem}_{timestamp}{video_save_path.suffix}'
                log.info(f'Using alternative filename: {video_save_path}')
        
        # Convert stereo processed audio to mono for video creation
        if processed_audio.dim() == 2 and processed_audio.shape[0] == 2:
            mono_audio = torch.mean(processed_audio, dim=0, keepdim=True)
        else:
            mono_audio = processed_audio
            
        # Use processed audio for video creation
        make_video(video_info, video_save_path, mono_audio, sampling_rate=seq_cfg.sampling_rate)
        log.info(f'Video with processed audio saved to {output_dir / video_save_path}')

    if device == 'cuda' and torch.cuda.is_available():
        log.info('Memory usage: %.2f GB', torch.cuda.max_memory_allocated() / (2**30))

    # Clean up temporary video file if created
    if temp_video_path and os.path.exists(temp_video_path):
        try:
            os.remove(temp_video_path)
            log.info(f'Cleaned up temporary video file: {temp_video_path}')
        except Exception as e:
            log.warning(f'Failed to clean up temporary video file: {e}')

    return save_path, video_save_path
    
