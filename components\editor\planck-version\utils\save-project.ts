// utils/save-project.ts
let lastVideoSrc: string | undefined;

export const getLastSavedVideoBuffer = async (): Promise<Buffer | null> => {
  if (!lastVideoSrc) {
    console.warn("❌ No videoSrc found in memory");
    return null;
  }

  try {
    const res = await fetch(lastVideoSrc);
    
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const reader = res.body?.getReader();
    if (!reader) {
      throw new Error("Response body is not readable");
    }

    const chunks: Uint8Array[] = [];
    let totalLength = 0;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      chunks.push(value);
      totalLength += value.length;
    }

    const buffer = new Uint8Array(totalLength);
    let position = 0;
    
    
    for (const chunk of chunks) {
      buffer.set(chunk, position);
      position += chunk.length;
    }

    return Buffer.from(buffer);
  } catch (err) {
    console.error("❌ Failed to convert video to buffer:", err);
    return null;
  }
};

export const saveVideoProject = async (videoSrc?: string) => {
  if (videoSrc) {
    lastVideoSrc = videoSrc; // ✅ just save
  }
};