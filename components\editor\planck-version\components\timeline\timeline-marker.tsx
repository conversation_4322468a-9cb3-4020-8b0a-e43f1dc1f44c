// FIXED: TimelineMarker with real-time drag updates
import React, { useState, useCallback, useRef, useEffect } from "react";

interface TimelineMarkerProps {
  currentFrame: number;
  totalDuration: number;
  onFrameChange?: (frame: number) => void;
  isPlaying?: boolean;
  fps?: number;
}

// Simple time formatting function using HH:MM:SS format
const formatTime = (
  timeInSeconds: number
): string => {
  const totalSeconds = Math.floor(timeInSeconds);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  // Use standard HH:MM:SS format
  return (
    `${hours.toString().padStart(2, "0")}:` +
    `${minutes.toString().padStart(2, "0")}:` +
    `${seconds.toString().padStart(2, "0")}`
  );
};

const TimelineMarker: React.FC<TimelineMarkerProps> = ({
  currentFrame,
  totalDuration,
  onFrameChange,
  isPlaying = false,
  fps = 30,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [displayFrame, setDisplayFrame] = useState(currentFrame);
  const markerRef = useRef<HTMLDivElement>(null);

  // Update display frame when currentFrame changes (but not during drag)
  useEffect(() => {
    if (!isDragging) {
      setDisplayFrame(currentFrame);
    }
  }, [currentFrame, isDragging]);

  // Listen for frame updates during playback
  useEffect(() => {
    const handleFrameUpdate = (event: CustomEvent) => {
      const { frame, isDragging: eventIsDragging } = event.detail;

      // Only update if not dragging
      if (!isDragging && !eventIsDragging) {
        setDisplayFrame(frame);
      }
    };

    window.addEventListener(
      "timeline-frame-update",
      handleFrameUpdate as EventListener
    );

    return () => {
      window.removeEventListener(
        "timeline-frame-update",
        handleFrameUpdate as EventListener
      );
    };
  }, [isDragging]);

  // Block all interactions during playback
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (isPlaying) {
        e.preventDefault();
        return;
      }

      if (!onFrameChange) return;
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(true);
      document.body.style.cursor = "grabbing";
    },
    [isPlaying, onFrameChange]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || isPlaying) return;

      const timelineContainer = document.querySelector(
        "[data-timeline-container]"
      ) as HTMLElement;
      if (!timelineContainer) return;

      const rect = timelineContainer.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
      const newFrame = Math.round((percentage / 100) * totalDuration);
      const clampedFrame = Math.max(0, Math.min(totalDuration, newFrame));

      // FIXED: Update display frame immediately during drag
      setDisplayFrame(clampedFrame);

      // FIXED: Dispatch real-time drag events for time display updates with formatted time
      const timeInSeconds = clampedFrame / fps;
      const formattedTime = formatTime(timeInSeconds);
      
      window.dispatchEvent(
        new CustomEvent("timeline-frame-update", {
          detail: { 
            frame: clampedFrame, 
            isDragging: true, 
            realTime: true,
            timeInSeconds,
            formattedTime,
            fps 
          },
        })
      );
    },
    [isDragging, totalDuration, isPlaying, fps]
  );

  const handleMouseUp = useCallback(() => {
    if (isDragging && !isPlaying) {
      setIsDragging(false);
      document.body.style.cursor = "";

      // FIXED: Final update when drag ends
      if (onFrameChange) {
        onFrameChange(displayFrame);
      }

      // FIXED: Dispatch final drag end event with formatted time
      const timeInSeconds = displayFrame / fps;
      const formattedTime = formatTime(timeInSeconds);
      
      window.dispatchEvent(
        new CustomEvent("timeline-frame-update", {
          detail: { 
            frame: displayFrame, 
            isDragging: false, 
            dragEnd: true,
            timeInSeconds,
            formattedTime,
            fps 
          },
        })
      );
    }
  }, [isDragging, displayFrame, onFrameChange, isPlaying, fps]);

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (isPlaying || isDragging) return;

      e.preventDefault();
      e.stopPropagation();

      const timelineContainer = document.querySelector(
        "[data-timeline-container]"
      ) as HTMLElement;
      if (!timelineContainer) return;

      const rect = timelineContainer.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
      const newFrame = Math.round((percentage / 100) * totalDuration);
      const clampedFrame = Math.max(0, Math.min(totalDuration, newFrame));

      setDisplayFrame(clampedFrame);

      if (onFrameChange) {
        onFrameChange(clampedFrame);
      }

      // Dispatch click event with formatted time
      const timeInSeconds = clampedFrame / fps;
      const formattedTime = formatTime(timeInSeconds);
      
      window.dispatchEvent(
        new CustomEvent("timeline-frame-update", {
          detail: { 
            frame: clampedFrame, 
            isDragging: false, 
            click: true,
            timeInSeconds,
            formattedTime,
            fps 
          },
        })
      );
    },
    [isDragging, totalDuration, onFrameChange, isPlaying, fps]
  );

  useEffect(() => {
    if (isDragging && !isPlaying) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp, isPlaying]);

  const positionPercent =
    totalDuration > 0 ? (displayFrame / totalDuration) * 100 : 0;

  return (
    <div
      ref={markerRef}
      className={`absolute top-0 z-50 pointer-events-auto ${
        isDragging
          ? "cursor-grabbing"
          : isPlaying
          ? "cursor-default"
          : "cursor-grab"
      }`}
      style={{
        left: `${positionPercent}%`,
        transform: "translateX(-50%)",
        height: "100%",
        willChange: isPlaying || isDragging ? "left" : "auto",
      }}
      onClick={handleClick}
      title={isPlaying ? "Pause to scrub timeline" : "Click or drag to scrub"}
    >
      <div
        className={`w-0.5 h-full bg-red-500 relative pointer-events-auto ${
          isDragging
            ? "cursor-grabbing"
            : isPlaying
            ? "cursor-default"
            : "cursor-grab"
        }`}
        onMouseDown={handleMouseDown}
      >
        {/* Triangle playhead */}
        <div
          className="absolute -top-0 left-1/2 transform -translate-x-1/2 pointer-events-auto"
          style={{
            width: 0,
            height: 0,
            borderLeft: "6px solid transparent",
            borderRight: "6px solid transparent",
            borderTop: "8px solid #ef4444",
          }}
        />

        {/* Drag indicator */}
        {isDragging && (
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-red-700 rounded-full animate-pulse" />
        )}

        {/* Playing indicator */}
        {isPlaying && (
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-red-600 rounded-full opacity-75" />
        )}

        <div
          className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 pointer-events-auto"
          style={{
            background: "transparent",
            cursor: isPlaying ? "default" : "grab",
          }}
          onMouseDown={handleMouseDown}
        />
      </div>
    </div>
  );
};

export default TimelineMarker;
