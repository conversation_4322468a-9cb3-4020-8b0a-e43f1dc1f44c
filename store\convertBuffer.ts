// store/projectStore.ts
import { create } from 'zustand';

interface ProjectBufferState {
  videoBuffer: ArrayBuffer | null;
  setVideoBuffer: (buffer: ArrayBuffer | null) => void;
  videoUrl: string | null;
  setVideoUrl: (url: string | null) => void;
}

export const useProjectBufferStore = create<ProjectBufferState>((set) => ({
  videoBuffer: null,
  setVideoBuffer: (buffer: ArrayBuffer | null) => set({ videoBuffer: buffer }),
  videoUrl: null,
  setVideoUrl: (url: string | null) => set({ videoUrl: url }),
}));
