#!/usr/bin/env python3
"""
Minimal build script - start with basic imports and add more if needed
"""
import subprocess
import sys
import os
import shutil

def run_command(cmd):
    """Run command and handle errors"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        sys.exit(1)
    
    print(result.stdout)
    return result


def main():
    print("🎵 Building Sound Post Server...")
    
    # Check if this is a dry run
    dry_run = len(sys.argv) > 1 and sys.argv[1] == "--dry-run"
    if dry_run:
        print("🧪 Dry run mode - validating configuration only")
    
    # Check if required files exist
    required_files = ["sound_post_server.py", "mmaudio_helper.py", "video_auto_cut.py"]
    print(f"Required files: {required_files}")
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Error: {file} not found in current directory")
            print(f"Current directory: {os.getcwd()}")
            print(f"Files found: {os.listdir('.')}")
            return
    print("✅ All required files found")
    # Create deployment folder
    deployment_dir = "deployment"
    os.makedirs(deployment_dir, exist_ok=True)
    
    # Clean previous builds
    for folder in ["dist", "build"]:
        folder_path = os.path.join(deployment_dir, folder)
        if os.path.exists(folder_path):
            shutil.rmtree(folder_path)
    
    # Get absolute paths to avoid path issues
    current_dir = os.getcwd()
    
    # Build PyInstaller command with virtual environment support
    cmd = [
        "pyinstaller",
        "--onefile",
        "--name", "sound-post-server",
        "--distpath", os.path.join(current_dir, deployment_dir, "dist"),
        "--workpath", os.path.join(current_dir, deployment_dir, "build"),
        "--specpath", deployment_dir,
        "sound_post_server.py"
    ]
    
    
    # Add required Python files
    required_py_files = ["mmaudio_helper.py", "video_auto_cut.py"]
    for py_file in required_py_files:
        file_path = os.path.join(current_dir, py_file)
        if os.path.exists(file_path):
            cmd.extend(["--add-data", f"{file_path}{os.pathsep}."])
            print(f"✅ Adding: {py_file}")
        else:
            print(f"⚠️  Warning: {py_file} not found")
    
    # Use --collect-all for open_clip (much simpler!)
    cmd.extend(["--collect-all", "open_clip"])
    print("✅ Using --collect-all for open_clip (includes all data files and configs)")
    
    # Add required folders (excluding large model files)
    print("🔍 Checking folder sizes...")
    
    # Add post_processing and mmaudio (smaller folders)
    small_folders = ["post_processing", "mmaudio"]
    for folder in small_folders:
        folder_path = os.path.join(current_dir, folder)
        if os.path.exists(folder_path) and os.path.isdir(folder_path):
            cmd.extend(["--add-data", f"{folder_path}{os.pathsep}{folder}"])
            print(f"✅ Adding folder: {folder}")
        else:
            print(f"⚠️  Warning: {folder} folder not found")
    
    # Handle large model folders differently - create info files instead
    large_folders = ["weights", "ext_weights"]
    for folder in large_folders:
        folder_path = os.path.join(current_dir, folder)
        if os.path.exists(folder_path) and os.path.isdir(folder_path):
            # Create a models info file instead of including huge files
            info_file = f"{folder}_info.txt"
            info_path = os.path.join(current_dir, info_file)
            with open(info_path, 'w') as f:
                f.write(f"# {folder.upper()} MODELS DIRECTORY\n")
                f.write(f"# This executable expects {folder} to be in the same directory\n")
                f.write(f"# Required files:\n")
                for file in os.listdir(folder_path):
                    if file.endswith(('.pth', '.pt', '.ckpt')):
                        f.write(f"# - {file}\n")
            cmd.extend(["--add-data", f"{info_path}{os.pathsep}."])
            print(f"📝 Created {info_file} (models will be loaded externally)")
            print(f"⚠️  NOTE: {folder}/ must be copied alongside the executable!")
        else:
            print(f"⚠️  Warning: {folder} folder not found")
    
    # Add essential hidden imports (including missing ones from error analysis)
    hidden_imports = [
        # Core server modules
        "uvicorn.logging",
        "uvicorn.server",
        "fastapi",
        "pydantic",
        
        # Custom modules  
        "mmaudio_helper", 
        
        # Deep learning frameworks
        "torch",
        "torch.nn",
        "torch.nn.functional",
        "transformers",
        "einops",
        
        # Audio processing
        "librosa",
        "librosa.core",
        "librosa.feature", 
        "torchaudio",
        "resampy",
        
        # Video processing
        "moviepy.editor",
        "cv2",
        "av",
        
        # Scientific computing
        "numpy",
        "scipy",
        "scipy.signal",
        "matplotlib",
        
        # Logging (MISSING - this was causing the error!)
        "colorlog",
        "logging",
        
        # File handling
        "aaf2", 
        "wave",
        "json",
        "pathlib",
        "requests",
        
        # Additional requirements.txt dependencies (CRITICAL MISSING MODULES)
        "tqdm",
        "omegaconf", 
        "protobuf",
        "torchdiffeq",
        "open_clip_torch",  # THIS WAS MISSING - causing the error!
        "open_clip",        # Alternative import name
        "clip",             # Another possible import
        "open_clip.factory",
        "open_clip.tokenizer",
        "open_clip.model_configs",
        "python-multipart",
        "multipart",
        "scenedetect",
        "pydub",
        "ffmpeg",
        "pyaaf2",
        
        # Additional ML/AI modules that might be missing
        "timm",
        "ftfy", 
        "regex",
        
        # Standard library (just in case)
        "fractions",
        "math",
        "time",
        "datetime",
        "sys",
        "os",
        "shutil",
        "subprocess"
    ]
    
    for import_name in hidden_imports:
        cmd.extend(["--hidden-import", import_name])
    
    print("Building with improved configuration...")
    print("If you get ModuleNotFoundError, we'll add more hidden imports")
    
    if dry_run:
        print("🔍 Command that would be executed:")
        print(" ".join(cmd))
        print("✅ Dry run completed - configuration looks good!")
        return
    
    # Run PyInstaller (assumes environment is already activated)
    run_command(cmd)
    
    # Check result
    exe_path = os.path.join(deployment_dir, "dist", 
                           "sound-post-server.exe" if os.name == 'nt' else "sound-post-server")
    
    if os.path.exists(exe_path):
        print(f"\n✅ Executable build successful!")
        print(f"📁 Executable: {exe_path}")
        
        # Automatically copy required model directories
        print(f"\n🔄 Copying required model directories...")
        dist_dir = os.path.join(deployment_dir, "dist")
        
        # Copy weights and ext_weights directories
        large_folders = ["weights", "ext_weights"]
        for folder in large_folders:
            source_path = os.path.join(current_dir, folder)
            dest_path = os.path.join(dist_dir, folder)
            
            if os.path.exists(source_path) and os.path.isdir(source_path):
                if os.path.exists(dest_path):
                    shutil.rmtree(dest_path)  # Remove existing copy
                shutil.copytree(source_path, dest_path)
                print(f"✅ Copied {folder}/ to deployment directory")
            else:
                print(f"⚠️  Warning: {folder} folder not found, skipping")
        
        print(f"\n🎉 Build complete! All files ready in: {dist_dir}")
        print(f"\n🧪 Test it:")
        print(f"1. cd {dist_dir}")
        print(f"2. Run: ./sound-post-server")
        print(f"3. Visit: http://localhost:8000/health")
    else:
        print(f"\n❌ Build failed")

if __name__ == "__main__":
    main()