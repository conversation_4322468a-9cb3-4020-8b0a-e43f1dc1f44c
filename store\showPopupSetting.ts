import { create } from "zustand";

interface TypeShowPopupSetting {
    showPopup: String;
    setShowPopup: (item: any) => void;
    isAnalyse: boolean;
    setIsAnalyse: (item: any) => void;
}

export const showPopupSetting = create<TypeShowPopupSetting>((set) => ({
    showPopup: "ai",
    isAnalyse: false,
    setShowPopup: (item) => set({ showPopup: item }),
    setIsAnalyse: (item) => set({ isAnalyse: item }),
}));
