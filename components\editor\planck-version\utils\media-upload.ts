/**
 * Uploads a media file to the server
 */
export const uploadMediaFile = async (file: File, userId: string): Promise<{ url: string; id: string }> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('userId', userId);

  try {
    const response = await fetch('/api/media/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload file');
    }

    const data = await response.json();
    return {
      url: data.url,
      id: data.id,
    };
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
};

/**
 * Deletes a media file from the server
 */
export const deleteMediaFile = async (fileId: string, userId: string): Promise<void> => {
  try {
    const response = await fetch(`/api/media/${fileId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete file');
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
};

/**
 * Gets video metadata including FPS
 */
export const getVideoMetadata = async (
  file: File
): Promise<{ duration: number; fps: number; width: number; height: number } | undefined> => {
  if (!file.type.startsWith("video/")) {
    return undefined;
  }

  return new Promise((resolve) => {
    const video = document.createElement('video');
    
    // Set timeout to handle cases where video loading hangs
    const timeoutId = setTimeout(() => {
      console.warn("Video metadata detection timed out");
      URL.revokeObjectURL(video.src);
      resolve(undefined);
    }, 10000); // Increased timeout for high-res videos

    // Additional event listeners for better FPS detection
    let decodedFrames = 0;
    let lastTime = 0;
    let frameTimes: number[] = [];
    
    video.preload = "metadata";
    
    video.addEventListener('timeupdate', () => {
      const currentTime = video.currentTime;
      if (currentTime > lastTime) {
        decodedFrames++;
        frameTimes.push(currentTime);
      }
      lastTime = currentTime;
    });

    video.onloadedmetadata = () => {
      clearTimeout(timeoutId);
      
      try {
        // Extract basic metadata
        const duration = video.duration;
        const width = video.videoWidth;
        const height = video.videoHeight;
        
        // Calculate FPS using multiple methods
        let fps = 30; // Default fallback
        
        // Method 1: Try to get from filename pattern (common in screen recordings)
        const fileNameFpsMatch = file.name.match(/(\d+)fps/i);
        if (fileNameFpsMatch) {
          fps = parseInt(fileNameFpsMatch[1], 10);
          console.log(`FPS detected from filename: ${fps}`);
        }
        
        // Method 2: Use frame timing data if available
        if (frameTimes.length > 10) {
          const timeDifferences = [];
          for (let i = 1; i < frameTimes.length; i++) {
            timeDifferences.push(frameTimes[i] - frameTimes[i-1]);
          }
          const averageFrameTime = timeDifferences.reduce((a, b) => a + b, 0) / timeDifferences.length;
          const calculatedFps = 1 / averageFrameTime;
          
          // Only use if it's a reasonable value and matches common FPS standards
          if (calculatedFps > 10 && calculatedFps < 120) {
            fps = Math.round(calculatedFps * 100) / 100;
            console.log(`FPS calculated from frame timing: ${fps}`);
          }
        }
        
        // Method 3: Use decoded frames count if available
        if (decodedFrames > 0 && duration > 0) {
          const calculatedFps = decodedFrames / duration;
          if (calculatedFps > 10 && calculatedFps < 120) {
            // If this is close to a standard FPS value, use it
            const standardFpsValues = [23.976, 24, 25, 29.97, 30, 50, 59.94, 60, 120];
            const closestStandard = standardFpsValues.reduce((prev, curr) => 
              Math.abs(curr - calculatedFps) < Math.abs(prev - calculatedFps) ? curr : prev
            );
            
            if (Math.abs(closestStandard - calculatedFps) < 1) {
              fps = closestStandard;
              console.log(`FPS calculated from frame count: ${fps}`);
            }
          }
        }
        
        // Method 4: Check for common FPS patterns in duration
        if (duration > 0) {
          const standardFpsValues = [23.976, 24, 25, 29.97, 30, 50, 59.94, 60, 120];
          const candidates = [];
          
          // Test each standard FPS to see which gives the most reasonable frame count
          for (const testFps of standardFpsValues) {
            const frameCount = Math.round(duration * testFps);
            const calculatedDuration = frameCount / testFps;
            const difference = Math.abs(calculatedDuration - duration);
            
            // Collect candidates that are close enough
            if (difference < 0.01) {
              candidates.push({ fps: testFps, difference, frameCount });
            }
          }
          
          if (candidates.length > 0) {
            // If multiple candidates are very close, prefer more common frame rates
            const commonFpsOrder = [30, 60, 24, 25, 29.97, 59.94, 23.976, 50, 120];
            
            // Find the best candidate, preferring common FPS values when differences are similar
            let selectedCandidate = candidates[0];
            for (const candidate of candidates) {
              const currentCommonIndex = commonFpsOrder.indexOf(selectedCandidate.fps);
              const candidateCommonIndex = commonFpsOrder.indexOf(candidate.fps);
              
              // If difference is significantly better, use it
              if (candidate.difference < selectedCandidate.difference * 0.5) {
                selectedCandidate = candidate;
              }
              // If differences are similar (within 2x), prefer more common FPS
              else if (candidate.difference < selectedCandidate.difference * 2 && 
                       candidateCommonIndex < currentCommonIndex) {
                selectedCandidate = candidate;
              }
            }
            
            // Additional validation: reject extremely high FPS for typical video durations
            if (selectedCandidate.fps >= 120 && duration > 5) {
              // Find next best candidate that's not high FPS
              const nonHighFpsCandidates = candidates.filter(c => c.fps < 120);
              if (nonHighFpsCandidates.length > 0) {
                const nextBest = nonHighFpsCandidates.reduce((best, current) => 
                  current.difference < best.difference ? current : best
                );
                fps = nextBest.fps;
              }
            } else {
              fps = selectedCandidate.fps;
            }
          }
        }
        
        // Final validation - if filename suggests 60fps but we got something else
        if (fileNameFpsMatch && Math.abs(parseInt(fileNameFpsMatch[1], 10) - fps) > 5) {
          console.warn(`FPS mismatch: filename suggests ${fileNameFpsMatch[1]}fps but detected ${fps}fps`);
          // In case of conflict, trust the filename if it's a standard value
          const filenameFps = parseInt(fileNameFpsMatch[1], 10);
          if ([60, 59.94, 50, 30, 29.97, 25, 24, 23.976].includes(filenameFps)) {
            fps = filenameFps;
          }
        }
        
        resolve({
          duration,
          fps: Math.round(fps * 100) / 100, // Round to 2 decimal places
          width,
          height
        });
      } catch (error) {
        console.error("Error extracting video metadata:", error);
        resolve({
          duration: video.duration,
          fps: 30, // Fallback
          width: video.videoWidth,
          height: video.videoHeight
        });
      } finally {
        URL.revokeObjectURL(video.src);
      }
    };

    video.onerror = () => {
      clearTimeout(timeoutId);
      console.error("Error loading video for metadata extraction");
      URL.revokeObjectURL(video.src);
      resolve(undefined);
    };

    video.src = URL.createObjectURL(file);
  });
};