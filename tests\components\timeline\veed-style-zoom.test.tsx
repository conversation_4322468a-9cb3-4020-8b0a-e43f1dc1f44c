import React from "react";
import { render, screen } from "@testing-library/react";
import { TimelineKeyframes } from "@/components/editor/planck-version/components/timeline/timeline-keyframes";
import { OverlayType } from "@/components/editor/planck-version/types";

// Mock the keyframe context
const mockKeyframeContext = {
  getKeyframes: jest.fn(),
  updateKeyframes: jest.fn(),
  clearKeyframes: jest.fn(),
  clearAllKeyframes: jest.fn(),
};

jest.mock(
  "@/components/editor/planck-version/contexts/keyframe-context",
  () => ({
    useKeyframeContext: () => mockKeyframeContext,
  })
);

// Mock the video audio waveform hook
jest.mock(
  "@/components/editor/planck-version/hooks/use-video-audio-waveform",
  () => ({
    useVideoAudioWaveform: () => ({
      waveformData: null,
      isLoading: false,
    }),
  })
);

// Mock the useKeyframes hook
jest.mock("@/components/editor/planck-version/hooks/use-keyframes", () => ({
  useKeyframes: () => ({
    frames: [
      { dataUrl: "data:image/jpeg;base64,frame1", frameNumber: 0 },
      { dataUrl: "data:image/jpeg;base64,frame2", frameNumber: 50 },
      { dataUrl: "data:image/jpeg;base64,frame3", frameNumber: 100 },
      { dataUrl: "data:image/jpeg;base64,frame4", frameNumber: 150 },
      { dataUrl: "data:image/jpeg;base64,frame5", frameNumber: 200 },
      { dataUrl: "data:image/jpeg;base64,frame6", frameNumber: 250 },
    ],
    previewFrames: [0, 50, 100, 150, 200, 250],
    isLoading: false,
  }),
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
}));

describe("Veed.io Style Zoom Behavior", () => {
  const mockVideoOverlay = {
    id: "test-video",
    type: OverlayType.VIDEO,
    src: "test-video.mp4",
    durationInFrames: 300,
    startFrame: 0,
    endFrame: 300,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show minimum number of thumbnails at low zoom", () => {
    // Mock container width
    const mockContainer = {
      clientWidth: 600,
    };

    jest.spyOn(React, "useRef").mockReturnValue({
      current: mockContainer,
    });

    render(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={0.5} // Low zoom
        onLoadingChange={jest.fn()}
      />
    );

    // At low zoom (0.5) with 600px width: (600 * 0.5) / 120 = 2.5 → 3 slots (minimum)
    const thumbnailContainers = screen.getAllByRole("img");
    expect(thumbnailContainers.length).toBeGreaterThanOrEqual(3);
    expect(thumbnailContainers.length).toBeLessThanOrEqual(5);
  });

  it("should show more thumbnails at high zoom", () => {
    // Mock container width
    const mockContainer = {
      clientWidth: 600,
    };

    jest.spyOn(React, "useRef").mockReturnValue({
      current: mockContainer,
    });

    render(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={3.0} // High zoom
        onLoadingChange={jest.fn()}
      />
    );

    // At high zoom (3.0) with 600px width: (600 * 3.0) / 120 = 15 slots
    const thumbnailContainers = screen.getAllByRole("img");
    expect(thumbnailContainers.length).toBeGreaterThan(10);
  });

  it("should respect maximum slot limit", () => {
    // Mock container width
    const mockContainer = {
      clientWidth: 1200,
    };

    jest.spyOn(React, "useRef").mockReturnValue({
      current: mockContainer,
    });

    render(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={10.0} // Very high zoom
        onLoadingChange={jest.fn()}
      />
    );

    // Should not exceed maximum of 20 slots
    const thumbnailContainers = screen.getAllByRole("img");
    expect(thumbnailContainers.length).toBeLessThanOrEqual(20);
  });

  it("should distribute available frames across slots efficiently", () => {
    const mockContainer = {
      clientWidth: 480, // Will result in 4 slots at 1x zoom
    };

    jest.spyOn(React, "useRef").mockReturnValue({
      current: mockContainer,
    });

    render(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={1.0}
        onLoadingChange={jest.fn()}
      />
    );

    // Should show thumbnails based on container width and zoom
    const thumbnailContainers = screen.getAllByRole("img");
    expect(thumbnailContainers.length).toBeGreaterThan(0);
  });

  it("should handle container width changes", () => {
    let mockContainer = {
      clientWidth: 300,
    };

    const mockRef = { current: mockContainer };
    jest.spyOn(React, "useRef").mockReturnValue(mockRef);

    const { rerender } = render(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={1.0}
        onLoadingChange={jest.fn()}
      />
    );

    const initialThumbnails = screen.getAllByRole("img");
    const initialCount = initialThumbnails.length;

    // Simulate container width change
    mockContainer.clientWidth = 600;

    rerender(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={1.0}
        onLoadingChange={jest.fn()}
      />
    );

    // Should adapt to new container width
    const newThumbnails = screen.getAllByRole("img");
    expect(newThumbnails.length).toBeGreaterThanOrEqual(initialCount);
  });

  it("should maintain consistent thumbnail size regardless of zoom", () => {
    const mockContainer = {
      clientWidth: 600,
    };

    jest.spyOn(React, "useRef").mockReturnValue({
      current: mockContainer,
    });

    const { rerender } = render(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={1.0}
        onLoadingChange={jest.fn()}
      />
    );

    const lowZoomThumbnails = screen.getAllByRole("img");
    const lowZoomCount = lowZoomThumbnails.length;

    rerender(
      <TimelineKeyframes
        overlay={mockVideoOverlay}
        currentFrame={0}
        zoomScale={2.0} // Higher zoom
        onLoadingChange={jest.fn()}
      />
    );

    const highZoomThumbnails = screen.getAllByRole("img");
    const highZoomCount = highZoomThumbnails.length;

    // Higher zoom should show more thumbnails, not larger ones
    expect(highZoomCount).toBeGreaterThan(lowZoomCount);

    // Thumbnail containers should maintain consistent styling
    highZoomThumbnails.forEach((thumbnail) => {
      expect(thumbnail).toHaveClass("object-cover");
    });
  });
});
