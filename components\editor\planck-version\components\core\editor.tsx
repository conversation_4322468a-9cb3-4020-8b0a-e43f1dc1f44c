"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./menu-handler";
import { useEditorContext } from "../../contexts/editor-context";
import { TimelineControls } from "../timeline/timeline-controls";
import { DISABLE_MOBILE_LAYOUT, FPS } from "../../constants";
import Timeline from "../timeline/timeline";
import { VideoPlayer } from "./video-player";
import { getEffectiveFPS } from "../../utils/fps-utils";

export const Editor: React.FC = () => {
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  React.useEffect(() => {
    const handleResize = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
    };

    handleResize();

    window.addEventListener("resize", handleResize);

    document.body.style.overflow = "hidden";
    document.documentElement.style.overflow = "hidden";

    return () => {
      window.removeEventListener("resize", handleResize);
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
    };
  }, []);

  const {
    overlays,
    selectedOverlayId,
    setSelectedOverlayId,
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    handleOverlayChange,
    handleTimelineClick,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    durationInFrames,
    setOverlays,
    handleFilesAdded,
    isProcessing,
  } = useEditorContext();

  // FINAL FIX - Direct frame seeking (no time conversion)
  const setCurrentFrame = React.useCallback(
    (frame: number) => {
      // ONLY seek if we're not playing (i.e., this is a manual seek)
      if (!isPlaying && playerRef.current) {
        // Get current frame before seek
        const beforeSeek = Math.round(playerRef.current.getCurrentFrame());
        try {
          // THE FIX: Pass frame directly, not converted to seconds

          playerRef.current.seekTo(frame);

          // Quick verification
          setTimeout(() => {
            if (playerRef.current) {
              const afterSeek = Math.round(playerRef.current.getCurrentFrame());
            }
          }, 100);
        } catch (e) {
          console.log("Direct frame seek failed:", e);
        }
      }

      // Always dispatch the frame update for UI purposes
      if (typeof window !== "undefined") {
        window.dispatchEvent(
          new CustomEvent("timeline-frame-update", {
            detail: {
              frame,
              timestamp: Date.now(),
              isManualSeek: !isPlaying,
            },
          })
        );
      }
    },
    [playerRef, isPlaying]
  );

  if (isMobile && DISABLE_MOBILE_LAYOUT) {
    return (
      <div className="flex items-center justify-center h-screen bg-white dark:bg-gray-900 p-6">
        <div className="text-center text-gray-900 dark:text-white">
          <h2 className="text-xl font-bold mb-3">Planck Video Editor</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 font-light mb-4">
            Currently, Planck Video Editor is designed as a full-screen desktop
            experience. We&apos;re actively working on making it
            mobile-friendly! 👀
          </p>
        </div>
      </div>
    );
  }

  const handleSplitAtTime = (framePosition: number, timeData?: any) => {
    // Find the overlay at the current frame that should be split
    const overlayToSplit = overlays?.find(
      (overlay) =>
        overlay.from <= framePosition &&
        overlay.from + overlay.durationInFrames > framePosition
    );

    if (overlayToSplit) {
      // Use your existing split function
      splitOverlay(overlayToSplit.id, framePosition);
    } else {
      // Optionally show a message that no overlay was found at this position
      console.warn("No overlay found at the specified time position");
    }
  };

  // Check if player is empty (with safety check)
  const isEmpty = !overlays || overlays.length === 0;

  return (
    <div
      className="flex flex-col overflow-hidden main-layout"
      style={{
        height: "calc(var(--vh, 1vh) * 100)",
        maxHeight: "-webkit-fill-available",
      }}
    >
      {/* MenuHandler handles all menu events from Electron - no UI rendering */}
      <MenuHandler />
      <div className="flex-grow flex flex-col lg:flex-row overflow-hidden">
        <VideoPlayer
          playerRef={playerRef}
          onFilesAdded={handleFilesAdded}
          isProcessing={isProcessing}
        />
      </div>

      {!isEmpty && (
        <>
          <TimelineControls
            isPlaying={isPlaying}
            togglePlayPause={togglePlayPause}
            currentFrame={currentFrame}
            totalDuration={durationInFrames}
            setCurrentFrame={setCurrentFrame}
            fps={getEffectiveFPS(overlays || [])}
            overlays={overlays || []}
            onSplitAtTime={handleSplitAtTime}
          />
          <Timeline
            currentFrame={currentFrame}
            overlays={overlays || []}
            durationInFrames={durationInFrames}
            selectedOverlayId={selectedOverlayId}
            setSelectedOverlayId={setSelectedOverlayId}
            onOverlayChange={handleOverlayChange}
            onOverlayDelete={deleteOverlay}
            onOverlayDuplicate={duplicateOverlay}
            onSplitOverlay={splitOverlay}
            setCurrentFrame={setCurrentFrame}
            setOverlays={setOverlays}
            onTimelineClick={handleTimelineClick}
          />
        </>
      )}
    </div>
  );
};
