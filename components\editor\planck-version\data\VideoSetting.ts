import video from "../components/icon/video.svg";
import audio from "../components/icon/audio.svg";
interface SelectOption {
  label: string;
  value: string;
  icon?: string;
}

export const FootwearItems: SelectOption[] = [
  {
    label: "Barefoot",
    value: "barefoot",
  },
  {
    label: "Hard sole shoes",
    value: "hard sole shoes",
  },
  {
    label: "Sneakers",
    value: "sneakers",
  },
  {
    label: "Flip flops/slippers",
    value: "flip flops/slippers",
  },
  {
    label: "Sandals",
    value: "sandals",
  },
  {
    label: "High heels",
    value: "high heels",
  },
  {
    label: "Clogs",
    value: "clogs",
  },
];

export const GroundMaterial_Indoor_Soft_Items: SelectOption[] = [
  {
    label: "-- Select --",
    value: "-",
  },
  {
    label: "Carpet",
    value: "carpet",
  },
  {
    label: "Rubber",
    value: "rubber",
  },
];

export const GroundMaterial_Indoor_Hard_Items: SelectOption[] = [
  {
    label: "-- Select --",
    value: "-",
  },
  {
    label: "Concrete",
    value: "concrete",
  },
  {
    label: "Tile",
    value: "tile",
  },
  {
    label: "Wood",
    value: "wood",
  },
  {
    label: "Metal",
    value: "metal",
  },
];

export const GroundMaterial_Outdoor_Soft_Items: SelectOption[] = [
  {
    label: "-- Select --",
    value: "-",
  },
  {
    label: "Grass",
    value: "grass",
  },
  {
    label: "Soil",
    value: "soil",
  },
  {
    label: "Sand",
    value: "sand",
  },
  {
    label: "Rubber",
    value: "rubber",
  },
  {
    label: "Gravel",
    value: "gravel",
  },
];
export const GroundMaterial_Outdoor_Hard_Items: SelectOption[] = [
  {
    label: "-- Select --",
    value: "-",
  },
  {
    label: "Asphalt",
    value: "asphalt",
  },
  {
    label: "Concrete/tile",
    value: "concrete/tile",
  },
  {
    label: "Stones/pebbles",
    value: "stones/pebbles",
  },
  {
    label: "Metal",
    value: "metal",
  }
];

export const GroundTextureItems: SelectOption[] = [
  {
    label: "Soft",
    value: "soft",
  },
  {
    label: "Hard",
    value: "hard",
  },
];

export const weatherItems: SelectOption[] = [
  {
    label: "Dry",
    value: "dry",
  },
  {
    label: "Wet",
    value: "wet",
  },
];

export const envItem: SelectOption[] = [
  {
    label: "Indoor",
    value: "indoor",
  },
  {
    label: "Outdoor",
    value: "outdoor",
  },
];

export const VideoTypeItems: SelectOption[] = [
  {
    label: "MP4",
    value: "mp4",
    icon: video,
  },
];

export const AudioTypeItems: SelectOption[] = [
  {
    label: "WAV",
    value: "wav",
    icon: audio,
  },
  {
    label: "AAF",
    value: "aaf",
    icon: audio,
  },
];

// Export the type for use in other components
export type { SelectOption };