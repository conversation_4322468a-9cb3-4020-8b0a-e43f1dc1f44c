import { useState, useEffect, useRef, useCallback } from "react";
import { PlayerRef } from "@remotion/player";
import { FPS } from "../constants";
import { getEffectiveFPS } from "../utils/fps-utils"; // Add this import

/**
 * Custom hook for managing video player functionality
 * Following the original architecture where frame updates come from external events
 * Now supports both 30fps and 60fps videos properly
 * @param overlays - Array of overlays (used to determine effective FPS)
 * @param durationInFrames - Duration in frames
 * @returns An object containing video player controls and state
 */
export const useVideoPlayer = (
  overlays: any[] = [],
  durationInFrames: number
) => {
  // State management
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentFrame, setCurrentFrame] = useState(0);
  const playerRef = useRef<PlayerRef>(null);
  const prevFrame = useRef<number>(-1); // Track previous frame to detect stuck player
  const prevPrevFrame = useRef<number>(-1); // Track frame before previous to be more sure about stuck detection

  // 🔥 NEW: Get effective FPS dynamically
  const effectiveFPS = overlays.length > 0 ? getEffectiveFPS(overlays) : FPS;

  // Listen to player's native events to sync isPlaying state
  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleTimeUpdate = () => {
      // Additional fallback to sync state
      if (player.getCurrentFrame) {
        const frame = Math.round(player.getCurrentFrame());
        setCurrentFrame(frame);
      }
    };

    // Add event listeners
    try {
      player.addEventListener("play", handlePlay);
      player.addEventListener("pause", handlePause);
      player.addEventListener("timeupdate", handleTimeUpdate);
    } catch (error) {
      console.error("❌ Failed to add player event listeners:", error);
    }

    return () => {
      try {
        player.removeEventListener("play", handlePlay);
        player.removeEventListener("pause", handlePause);
        player.removeEventListener("timeupdate", handleTimeUpdate);
      } catch (error) {
        console.error("❌ Failed to remove player event listeners:", error);
      }
    };
  }, []);

  // Frame update effect - sync with player during playback
  useEffect(() => {
    let animationFrameId: number;
    let lastUpdateTime = 0;
    // 🔥 FIXED: Use effective FPS for frame update interval
    const frameInterval = 1000 / effectiveFPS; // Dynamic FPS instead of fixed 30fps

    const updateCurrentFrame = () => {
      const now = performance.now();
      if (now - lastUpdateTime >= frameInterval) {
        if (playerRef.current) {
          const frame = Math.round(playerRef.current.getCurrentFrame());
          setCurrentFrame(frame);

          // 🔥 FIXED: Proper end detection logic for both 30fps and 60fps
          const veryNearEnd = frame >= durationInFrames - 2; // Allow 2 frame buffer instead of 1
          const potentialNaturalEnd = frame >= durationInFrames * 0.9; // 90% threshold instead of fixed 500
          const definitelyStuck =
            frame > 50 &&
            frame === prevFrame.current &&
            prevFrame.current === prevPrevFrame.current;

          // Only trigger end if we're really at the metadata end OR clearly stuck for multiple frames
          if (
            isPlaying &&
            (veryNearEnd || (potentialNaturalEnd && definitelyStuck))
          ) {
            setIsPlaying(false);
            if (playerRef.current) {
              playerRef.current.pause();
            }

            window.dispatchEvent(
              new CustomEvent("timeline-frame-update", {
                detail: {
                  frame: Math.min(frame, durationInFrames),
                  isDragging: false,
                  videoEnded: true,
                  pauseComplete: true,
                },
              })
            );
            return;
          }

          // Track previous frames to detect when player gets stuck
          prevPrevFrame.current = prevFrame.current;
          prevFrame.current = frame;

          // Dispatch timeline update for UI synchronization during playback
          if (isPlaying) {
            window.dispatchEvent(
              new CustomEvent("timeline-frame-update", {
                detail: {
                  frame,
                  isDragging: false,
                  isPlaying: true,
                  minimal: true,
                },
              })
            );
          }
        }
        lastUpdateTime = now;
      }

      if (isPlaying) {
        animationFrameId = requestAnimationFrame(updateCurrentFrame);
      }
    };

    if (isPlaying) {
      animationFrameId = requestAnimationFrame(updateCurrentFrame);
    }

    // Clean up
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isPlaying, durationInFrames, effectiveFPS]); // Add effectiveFPS to dependencies

  /**
   * Starts playing the video
   */
  const play = useCallback(() => {
    if (playerRef.current) {
      try {
        // Get the actual current frame from the player instead of using stale state
        const actualCurrentFrame = Math.round(
          playerRef.current.getCurrentFrame()
        );

        // 🔥 FIXED: Use dynamic threshold based on effective FPS
        // More conservative threshold - allow more buffer for 60fps videos
        const threshold = Math.max(
          durationInFrames - effectiveFPS / 10, // Buffer based on FPS (6 frames for 60fps, 3 for 30fps)
          durationInFrames * 0.97 // 97% threshold
        );

        // If we're at or near the end, restart from beginning
        if (actualCurrentFrame >= threshold) {
          playerRef.current.seekTo(0);
          setCurrentFrame(0);
          // Dispatch restart event
          window.dispatchEvent(
            new CustomEvent("timeline-frame-update", {
              detail: {
                frame: 0,
                isDragging: false,
                seekComplete: true,
                videoRestart: true,
              },
            })
          );
        }

        playerRef.current.play();
        // Manually set state as backup in case events don't fire
        setTimeout(() => {
          setIsPlaying(true);
        }, 100);
      } catch (error) {
        console.error("Error calling play:", error);
      }
    } else {
      console.error("PlayerRef is null");
    }
  }, [durationInFrames, effectiveFPS]); // Add effectiveFPS to dependencies

  /**
   * Pauses the video
   */
  const pause = useCallback(() => {
    if (playerRef.current) {
      try {
        // Get current frame before pausing
        const currentPlayerFrame = Math.round(
          playerRef.current.getCurrentFrame()
        );

        playerRef.current.pause();

        // Manually set state as backup in case events don't fire
        setTimeout(() => {
          setIsPlaying(false);
        }, 100);

        // Update frame and dispatch event
        setCurrentFrame(currentPlayerFrame);
        window.dispatchEvent(
          new CustomEvent("timeline-frame-update", {
            detail: {
              frame: currentPlayerFrame,
              isDragging: false,
              pauseComplete: true,
            },
          })
        );
      } catch (error) {
        console.error("Error calling pause:", error);
      }
    } else {
      console.error("PlayerRef is null");
    }
  }, []);

  /**
   * Toggles between play and pause states
   */
  const togglePlayPause = useCallback(() => {
    if (playerRef.current) {
      const currentPlayerFrame = Math.round(
        playerRef.current.getCurrentFrame()
      );
      if (!isPlaying) {
        play();
      } else {
        pause();
      }
    } else {
      console.error("❌ PlayerRef is null in togglePlayPause");
    }
  }, [isPlaying, play, pause]);

  /**
   * Converts frame count to formatted time string
   * 🔥 FIXED: Use effective FPS instead of fixed FPS
   * @param frames - Number of frames to convert
   * @returns Formatted time string in MM:SS format
   */
  const formatTime = useCallback(
    (frames: number) => {
      const totalSeconds = frames / effectiveFPS; // Use effective FPS
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = Math.floor(totalSeconds % 60);
      const frameInSecond = Math.floor(frames % effectiveFPS) // Use effective FPS
        .toString()
        .padStart(2, "0");

      return `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}.${frameInSecond}`;
    },
    [effectiveFPS]
  ); // Add effectiveFPS to dependencies

  /**
   * Seeks to a specific frame in the video
   * 🔥 FIXED: Use effective FPS for seek calculations
   * @param frame - Target frame number
   */
  const seekTo = useCallback(
    (frame: number) => {
      if (playerRef.current) {
        // 🔥 FIXED: Convert frame to seconds using effective FPS
        const timeInSeconds = frame / effectiveFPS; // Use effective FPS
        const beforeSeekFrame = Math.round(playerRef.current.getCurrentFrame());

        setCurrentFrame(frame);
        playerRef.current.seekTo(timeInSeconds);

        // Dispatch seek event to update timeline
        window.dispatchEvent(
          new CustomEvent("timeline-frame-update", {
            detail: {
              frame,
              isDragging: false,
              seekComplete: true,
            },
          })
        );
      }
    },
    [effectiveFPS] // Use effectiveFPS instead of durationInFrames
  );

  return {
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    play,
    pause,
    seekTo,
  };
};
