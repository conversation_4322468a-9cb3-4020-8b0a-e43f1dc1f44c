import { useEffect } from "react";
import { useTimeline } from "../../contexts/timeline-context";

interface TimelineAIAudioListenerProps {
  overlays: any[];
  onOverlayChange?: (overlay: any) => void;
}

/**
 * Component that listens for AI audio placement events and ensures proper audio playback
 */
export const TimelineAIAudioListener: React.FC<
  TimelineAIAudioListenerProps
> = ({ overlays, onOverlayChange }) => {
  const { addRow, visibleRows, setVisibleRows } = useTimeline();

  useEffect(() => {
    const handleAIAudioAdded = (event: CustomEvent) => {
      const {
        row,
        segmentId,
        startFrame,
        durationFrames,
        audioDurationFrames,
        segmentDurationFrames,
        audioFile,
        audioSrc,
        audioElement,
      } = event.detail;

      console.log("🎵 Timeline received AI audio added event:", event.detail);

      // Ensure timeline has enough rows to show the AI audio
      const requiredRows = row + 1; // +1 because rows are 0-indexed
      if (requiredRows > visibleRows) {
        console.log(
          `📈 Expanding timeline to ${requiredRows} rows for AI audio`
        );
        setVisibleRows(requiredRows);
      }

      // 🔥 SETUP AUDIO PLAYBACK CONTEXT
      if (audioFile && audioSrc) {
        console.log("🎵 Setting up audio playback context...");

        try {
          // Create a persistent audio context for this AI audio
          const audioContext = new (window.AudioContext ||
            (window as any).webkitAudioContext)();

          // Store audio information globally for the video player to access
          (window as any).aiAudioRegistry =
            (window as any).aiAudioRegistry || {};
          (window as any).aiAudioRegistry[segmentId] = {
            audioFile: audioFile,
            audioSrc: audioSrc,
            audioContext: audioContext,
            row: row,
            startFrame: startFrame,
            durationFrames: durationFrames,
            volume: 1,
            ready: true,
          };

          console.log("✅ AI audio registered for playback:", {
            segmentId: segmentId,
            row: row,
            startFrame: startFrame,
            durationFrames: durationFrames,
            audioSrc: audioSrc.substring(0, 50) + "...",
          });

          // 🔥 FORCE AUDIO PRELOAD
          const preloadAudio = document.createElement("audio");
          preloadAudio.src = audioSrc;
          preloadAudio.preload = "auto";
          preloadAudio.volume = 1;

          preloadAudio.oncanplaythrough = () => {
            console.log(
              "✅ AI audio preloaded and ready for immediate playback"
            );
            (window as any).aiAudioRegistry[segmentId].preloadedElement =
              preloadAudio;
          };

          preloadAudio.onerror = (error) => {
            console.error("❌ AI audio preload failed:", error);
          };

          // Start preloading
          preloadAudio.load();
        } catch (audioError) {
          console.error(
            "❌ Failed to setup audio playback context:",
            audioError
          );
        }
      }

      // Log the placement details
      console.log(`✅ AI Audio placed successfully:`);
      console.log(`   - Segment: ${segmentId}`);
      console.log(`   - Row: ${row}`);
      console.log(`   - Start Frame: ${startFrame}`);
      console.log(
        `   - Duration: ${durationFrames} frames (limited by segment)`
      );
      console.log(`   - Audio Duration: ${audioDurationFrames} frames`);
      console.log(`   - Segment Duration: ${segmentDurationFrames} frames`);
    };

    const handleTimelineRefresh = (event: CustomEvent) => {
      if (event.detail.reason === "ai-audio-placed") {
        console.log("🔄 Timeline refresh triggered for AI audio placement");

        const { audioReady, optimalRows } = event.detail;

        if (audioReady) {
          console.log("🎵 AI audio is ready for playback");

          // Dispatch event to video player to refresh audio context
          window.dispatchEvent(
            new CustomEvent("video-player-refresh-audio", {
              detail: {
                reason: "ai-audio-ready",
                timestamp: Date.now(),
              },
            })
          );
        }

        // Clean up rows if specified
        if (optimalRows && optimalRows < visibleRows) {
          console.log(
            `🧹 Auto-cleaning timeline: ${visibleRows} → ${optimalRows} rows`
          );
          setVisibleRows(optimalRows);
        }
      }
    };

    // 🔥 NEW: Handle audio playback requests from video player
    const handleAudioPlaybackRequest = (event: CustomEvent) => {
      const { segmentId, currentTime, action } = event.detail;

      console.log(`🎵 Audio playback request:`, event.detail);

      const aiAudioInfo = (window as any).aiAudioRegistry?.[segmentId];
      if (!aiAudioInfo) {
        console.warn(`⚠️ No AI audio found for segment ${segmentId}`);
        return;
      }

      try {
        const { preloadedElement, startFrame, durationFrames } = aiAudioInfo;

        if (action === "play" && preloadedElement) {
          console.log(
            `🎵 Playing AI audio for segment ${segmentId} at time ${currentTime}`
          );
          preloadedElement.currentTime = 0; // Start from beginning
          preloadedElement.play();
        } else if (action === "pause" && preloadedElement) {
          console.log(`🎵 Pausing AI audio for segment ${segmentId}`);
          preloadedElement.pause();
        }
      } catch (playbackError) {
        console.error("❌ AI audio playback error:", playbackError);
      }
    };

    // Add event listeners
    window.addEventListener(
      "timeline-ai-audio-added",
      handleAIAudioAdded as EventListener
    );
    window.addEventListener(
      "timeline-refresh-required",
      handleTimelineRefresh as EventListener
    );
    window.addEventListener(
      "ai-audio-playback-request",
      handleAudioPlaybackRequest as EventListener
    );

    return () => {
      // Clean up event listeners
      window.removeEventListener(
        "timeline-ai-audio-added",
        handleAIAudioAdded as EventListener
      );
      window.removeEventListener(
        "timeline-refresh-required",
        handleTimelineRefresh as EventListener
      );
      window.removeEventListener(
        "ai-audio-playback-request",
        handleAudioPlaybackRequest as EventListener
      );
    };
  }, [overlays, visibleRows, setVisibleRows, onOverlayChange]);

  // This component doesn't render anything, it just listens for events
  return null;
};
