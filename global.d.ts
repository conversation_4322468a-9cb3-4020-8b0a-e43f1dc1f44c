// global.d.ts
export { };

declare global {
  interface Window {
    electronAPI?: {
      fileExists(path: string): boolean | PromiseLike<boolean>;
      
      clearProjectMetadata(): Promise<{
        success: boolean;
        message: string;
      }>;

      writeFile(path: string, data: string): void | PromiseLike<void>;
      readFileBuffer(path: string): ArrayBuffer | PromiseLike<ArrayBuffer>;
      getLastSavedMetadata(): any;
      saveVideo: () => void;
       openProject: () => Promise<{
        success: boolean;       
        data?: any;           // The parsed project data
        buffer?: ArrayBuffer; // Video buffer
        videoName?: string;   // Video filename
        dir?: string;         // Directory path
        filePath?: string;    // Path to .planck file
        audioFiles?: { [key: string]: string }; // Audio files map
        message?: string;
        projectData: any;
      } | null>;
      setProjectMetadata?: (metadata: {
        projectName: string;
        videoPath: string;
        savedPath: string;
        planckPath: string;
        timeFrameSegments: any[];
      }) => Promise<{ success: boolean; error?: string }>;
      minimizeWindow: () => void;
      maximize: () => void;
      closeWindow: () => void;
      runPythonAnalysis: (requestData: requestData) => Promise<void>;
      onMenuSave: (buffer: Buffer) => Promise<void>;
      
      onSaveAsVideo: (buffer: Buffer, metadata: object) => Promise<{ success: boolean; filePath?: string; metaPath?: string; message?: string }>;
      onSaveVideo: (buffer: Buffer | null, metadata: object) => Promise<{ success: boolean; filePath?: string; metaPath?: string; message?: string }>;
      
      // Auto cut functionality
      selectVideoFile: () => Promise<{
        success: boolean;
        filePath?: string;
        error?: string;
      }>;
      videoAutoCut: (config: any) => Promise<{
        success: boolean;
        data?: any;
        error?: string;
        err_msg: string;
      }>;
    };
  }
}