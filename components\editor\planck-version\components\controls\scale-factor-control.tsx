import React from "react";
import { useEditorContext } from "../../contexts/editor-context";

/**
 * ScaleFactorControl component provides UI controls for adjusting the video player scale factor
 * This allows users to make the video container larger or smaller within its available space
 */
export const ScaleFactorControl: React.FC = () => {
  const { scaleFactor, updateScaleFactor, getScaleFactor } = useEditorContext();

  const handleScaleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newScale = parseFloat(event.target.value);
    updateScaleFactor(newScale);
  };

  const presetScales = [
    { label: "Small", value: 0.5 },
    { label: "Medium", value: 0.7 },
    { label: "Large", value: 0.9 },
    { label: "Full", value: 1.0 },
  ];

  return (
    <div className="scale-factor-control p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
        Video Player Size
      </h3>
      
      {/* Slider Control */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Scale Factor: {Math.round(scaleFactor * 100)}%
        </label>
        <input
          type="range"
          min="0.1"
          max="1.0"
          step="0.05"
          value={scaleFactor}
          onChange={handleScaleChange}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
        />
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>10%</span>
          <span>100%</span>
        </div>
      </div>

      {/* Preset Buttons */}
      <div className="grid grid-cols-4 gap-2">
        {presetScales.map((preset) => (
          <button
            key={preset.label}
            onClick={() => updateScaleFactor(preset.value)}
            className={`px-3 py-2 text-sm rounded-md transition-colors ${
              Math.abs(scaleFactor - preset.value) < 0.01
                ? "bg-blue-500 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            {preset.label}
          </button>
        ))}
      </div>

      {/* Current Value Display */}
      <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
        Current scale: {getScaleFactor().toFixed(2)} ({Math.round(getScaleFactor() * 100)}%)
      </div>
    </div>
  );
};

/**
 * Example usage in a component:
 * 
 * import { ScaleFactorControl } from "./components/controls/scale-factor-control";
 * 
 * function MyEditorComponent() {
 *   return (
 *     <div>
 *       <ScaleFactorControl />
 *       // ... other components
 *     </div>
 *   );
 * }
 * 
 * Or you can use the scale factor functions directly:
 * 
 * function MyCustomComponent() {
 *   const { scaleFactor, updateScaleFactor, getScaleFactor } = useEditorContext();
 *   
 *   const makeVideoSmaller = () => updateScaleFactor(0.5);
 *   const makeVideoLarger = () => updateScaleFactor(0.9);
 *   const getCurrentScale = () => console.log("Current scale:", getScaleFactor());
 *   
 *   return (
 *     <div>
 *       <button onClick={makeVideoSmaller}>Make Smaller</button>
 *       <button onClick={makeVideoLarger}>Make Larger</button>
 *       <button onClick={getCurrentScale}>Log Current Scale</button>
 *       <p>Current scale: {scaleFactor}</p>
 *     </div>
 *   );
 * }
 */
