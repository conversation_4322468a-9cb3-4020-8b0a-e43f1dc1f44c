import { useState } from "react";
import { AppContext } from "./AppContext";

export default function AppProvider({ children }) {
  const [page, setPage] = useState("home");
  const [isAnalyseVideoDone, setIsAnalyseVideoDone] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  return (
    <AppContext.Provider
      value={{
        page,
        setPage,
        isAnalyseVideoDone,
        setIsAnalyseVideoDone,
        setIsAnalyseVideoDone,
        isLoading, setIsLoading
      }}
    >
      {children}
    </AppContext.Provider>
  );
}
