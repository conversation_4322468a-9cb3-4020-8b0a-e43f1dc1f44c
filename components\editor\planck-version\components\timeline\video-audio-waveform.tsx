import { memo, useMemo } from "react";

interface WaveformData {
  peaks: number[];
  length: number;
}

interface VideoAudioWaveformProps {
  waveformData: WaveformData;
  totalSlots: number;
  zoomScale?: number; // Add zoom scale prop
}

/**
 * Professional Dense VideoAudioWaveform Component
 *
 * Creates a high-density waveform visualization with hundreds of thin vertical bars
 * overlaid on video thumbnails in the timeline
 */
const VideoAudioWaveform = memo(
  ({ waveformData, totalSlots, zoomScale = 1 }: VideoAudioWaveformProps) => {
    // Calculate density based on zoom level - higher zoom = much denser waveform
    const targetBars = useMemo(() => {
      // Base density scales dramatically with zoom
      const baseDensity = Math.floor(totalSlots * 15 * Math.pow(zoomScale, 1.2));
      
      // Zoom level thresholds for different densities
      if (zoomScale <= 0.5) {
        // Very zoomed out - sparse waveform
        return Math.max(100, Math.min(baseDensity, 200));
      } else if (zoomScale <= 1) {
        // Normal zoom - medium density
        return Math.max(200, Math.min(baseDensity, 400));
      } else if (zoomScale <= 2) {
        // Zoomed in - high density
        return Math.max(400, Math.min(baseDensity, 800));
      } else {
        // Very zoomed in - ultra high density
        return Math.max(800, Math.min(baseDensity, 1500));
      }
    }, [totalSlots, zoomScale]);
    
    // Advanced sampling for smooth, dense waveform
    const densePeaks = useMemo(() => {
      if (waveformData.peaks.length === 0) return [];
      
      if (waveformData.peaks.length >= targetBars) {
        // Downsample using RMS windowing for better representation
        const windowSize = waveformData.peaks.length / targetBars;
        const result = [];
        
        for (let i = 0; i < targetBars; i++) {
          const start = Math.floor(i * windowSize);
          const end = Math.min(Math.floor((i + 1) * windowSize), waveformData.peaks.length);
          
          // Calculate RMS for this window
          let sum = 0;
          let count = 0;
          for (let j = start; j < end; j++) {
            sum += waveformData.peaks[j] * waveformData.peaks[j];
            count++;
          }
          
          const rms = count > 0 ? Math.sqrt(sum / count) : 0;
          result.push(rms);
        }
        
        return result;
      } else {
        // Upsample using interpolation
        const result = [];
        const ratio = waveformData.peaks.length / targetBars;
        
        for (let i = 0; i < targetBars; i++) {
          const sourceIndex = i * ratio;
          const leftIndex = Math.floor(sourceIndex);
          const rightIndex = Math.min(leftIndex + 1, waveformData.peaks.length - 1);
          const fraction = sourceIndex - leftIndex;
          
          // Linear interpolation
          const leftValue = waveformData.peaks[leftIndex] || 0;
          const rightValue = waveformData.peaks[rightIndex] || 0;
          const interpolated = leftValue + (rightValue - leftValue) * fraction;
          
          result.push(interpolated);
        }
        
        return result;
      }
    }, [waveformData.peaks, targetBars]);

    // Process peaks for professional appearance
    const processedBars = useMemo(() => {
      if (densePeaks.length === 0) return [];
      
      const maxPeak = Math.max(...densePeaks);
      const minHeight = 18; // Increased minimum height percentage
      const maxHeight = 96; // Increased maximum height percentage
      
      return densePeaks.map((peak, index) => {
        // Normalize peak (0-1)
        const normalized = peak / (maxPeak || 1);
        
        // Apply logarithmic curve for better visual distribution
        const curved = Math.pow(normalized, 0.65);
        
        // More uniform height distribution like the blue reference
        const height = minHeight + (curved * (maxHeight - minHeight));
        
        // Reduced variation for cleaner, more uniform appearance
        const variation = 1 + Math.sin(index * 0.05) * 0.02; // Much subtler variation
        const finalHeight = Math.min(height * variation, maxHeight);
        
        // Color intensity based on amplitude
        const intensity = Math.min(normalized * 1.2, 1);
        
        return {
          height: finalHeight,
          intensity: intensity
        };
      });
    }, [densePeaks]);

    // Calculate bar width for gapless, dense appearance
    const barWidth = useMemo(() => {
      // For true density, bars should fill 100% width with no gaps
      return 100 / processedBars.length; // Each bar takes exact percentage of space
    }, [processedBars.length]);

    // Calculate gap size - should be 0 or minimal for dense appearance
    const gapSize = useMemo(() => {
      if (zoomScale <= 0.5) {
        return 0.1; // Tiny gap when zoomed out
      } else {
        return 0; // No gaps when zoomed in for maximum density
      }
    }, [zoomScale]);

    return (
      <div className="absolute bottom-0 left-0 right-0 h-4 flex items-end overflow-hidden px-1">
        <div 
          className="flex items-end h-full w-full"
          style={{ 
            gap: `${gapSize}%` // Dynamic gap based on zoom
          }}
        >
          {processedBars.map((bar, index) => {
            // Single solid orange color for all bars - only height varies
            const getBarColor = () => {
              return "bg-orange-400"; // Single consistent orange color
            };

            return (
              <div
                key={index}
                className={`${getBarColor()} ${gapSize === 0 ? '' : 'rounded-t-[0.5px]'} shadow-sm flex-shrink-0`}
                style={{
                  height: `${bar.height}%`,
                  width: `${barWidth}%`,
                  minHeight: "2px",
                }}
              />
            );
          })}
        </div>
      </div>
    );
  }
);
VideoAudioWaveform.displayName = "VideoAudioWaveform";

export default VideoAudioWaveform;