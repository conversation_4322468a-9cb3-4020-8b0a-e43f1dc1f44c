// store/dataVideo.tsx
import { create } from 'zustand';

export interface Segment {
  segmentId: number;
  segmentName: string;
  startTime: number;
  endTime: number;
  isAnalyseVideo?: boolean;
  shortDescription?: string;
  weather?: any;
  environment?: any;
  processingTime?: any;
  segmentDurationSeconds?: any;
  videoSize?: any;
  groundTexture?: any;
  groundMaterial?: any;
  footwear?: any;
  fullPrompt?: any;
  characterSize?: any;
}

interface DataVideoState {
  segment: Segment | null;
  setSegment: (data: Segment | null) => void;
}

export const useDataVideoStore = create<DataVideoState>((set) => ({
  segment: null, // ✅ jangan pakai '', gunakan null
  setSegment: (data: Segment | null) => set({ segment: data }),
}));