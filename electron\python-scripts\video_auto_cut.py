#!/usr/bin/env python3
"""
Video Auto Cut - Exact implementation from https://github.com/Routhleck/video-auto-cut
Restructured for API usage while maintaining original algorithms
"""

import os
import cv2
import math
import logging
from typing import List, Dict

# Original repo dependencies - exact matches
try:
    from scenedetect import ContentDetector
    from scenedetect.video_manager import VideoManager
    from scenedetect.scene_manager import SceneManager
    from scenedetect.detectors import ContentDetector
    SCENEDETECT_AVAILABLE = True
except ImportError:
    SCENEDETECT_AVAILABLE = False
    print("⚠️ SceneDetect not available - install with: pip install scenedetect")

try:
    from moviepy.editor import VideoFileClip
    import numpy as np
    from pydub import AudioSegment
    from pydub.silence import detect_silence
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False
    print("⚠️ Audio processing libraries not available")

# Setup logging to match original repo
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoAutoCutOriginal:
    """
    Video Auto Cut analyzer - exact implementation from original repository
    Combines scene detection with audio analysis for intelligent video cutting
    """
    
    def __init__(self):
        """Initialize with original repo settings"""
        self.scene_detect_available = SCENEDETECT_AVAILABLE
        self.audio_processing_available = AUDIO_PROCESSING_AVAILABLE
        
        # Simplified for timestamp analysis only
        
    def get_video_duration(self, video_path: str) -> float:
        """Get video duration using OpenCV or MoviePy"""
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        try:
            # Try OpenCV first (more reliable)
            cap = cv2.VideoCapture(video_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                if fps > 0:
                    duration = frame_count / fps
                    cap.release()
                    logger.info(f"📹 Video duration: {duration:.2f}s")
                    return duration
                cap.release()
            
            # Fallback to MoviePy if available
            if self.audio_processing_available:
                from moviepy.editor import VideoFileClip
                with VideoFileClip(video_path) as video:
                    duration = video.duration
                logger.info(f"📹 Video duration: {duration:.2f}s")
                return duration
            else:
                raise Exception("No video processing library available")
                
        except Exception as e:
            logger.error(f"❌ Failed to get video duration: {e}")
            raise

    def get_scene_list(self, video_path: str, threshold: float = 30.0):
        """
        Scene detection using ContentDetector - exact from original repo scene_cut.py
        """
        if not self.scene_detect_available:
            raise Exception("SceneDetect library not available - install with: pip install scenedetect")
        
        try:
            logger.info(f"🔍 Starting scene detection with threshold: {threshold}")
            
            # Original repo approach using VideoManager and SceneManager
            video_manager = VideoManager([video_path])
            scene_manager = SceneManager()
            
            # Add ContentDetector with threshold (original repo default: 30.0)
            scene_manager.add_detector(ContentDetector(threshold=threshold))
            
            # Start the video manager and detect scenes
            video_manager.start()
            scene_manager.detect_scenes(frame_source=video_manager)
            scene_list = scene_manager.get_scene_list()
            
            video_manager.release()
            
            logger.info(f"✅ Scene detection found {len(scene_list)} scenes")
            return scene_list
            
        except Exception as e:
            logger.error(f"❌ Scene detection failed: {e}")
            raise

    def scene_rgb_detect(self, video_path: str):
        """
        RGB-based scene detection - exact from original repo scene_rgb_detect.py
        """
        try:
            logger.info("🎨 Starting RGB-based scene detection")
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError("Could not open video file")
            
            # Get video properties
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Resize to 15% for processing efficiency (original repo approach)
            new_width = int(width * 0.15)
            new_height = int(height * 0.15)
            
            scenes = []
            prev_frame = None
            scene_start = 0
            threshold = 460  # Original repo threshold
            
            frame_idx = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Resize frame to 15%
                frame_resized = cv2.resize(frame, (new_width, new_height))
                
                if prev_frame is not None:
                    # Calculate RGB difference
                    diff = cv2.absdiff(frame_resized, prev_frame)
                    diff_sum = np.sum(diff)
                    
                    # Scene change detection using threshold
                    if diff_sum > threshold:
                        # End current scene and start new one
                        scenes.append([scene_start, frame_idx])
                        scene_start = frame_idx
                        logger.info(f"🎬 Scene change detected at frame {frame_idx}")
                
                prev_frame = frame_resized.copy()
                frame_idx += 1
            
            # Add final scene
            if scene_start < frame_count:
                scenes.append([scene_start, frame_count])
            
            cap.release()
            
            logger.info(f"✅ RGB detection found {len(scenes)} scenes")
            return scenes
            
        except Exception as e:
            logger.error(f"❌ RGB scene detection failed: {e}")
            raise

    def find_silence_boundaries_simple(self, video_path: str, audio_tolerance: float = -40.0) -> list:
        """
        Simple silence detection for timestamp analysis only
        """
        if not self.audio_processing_available:
            raise Exception("Audio processing libraries not available - install moviepy, numpy, and pydub")
        
        try:
            logger.info("🎵 Finding silence boundaries for cut optimization")
            
            # Extract audio from video
            temp_audio_path = "temp_audio.wav"
            from moviepy.editor import VideoFileClip
            with VideoFileClip(video_path) as video:
                audio = video.audio
                if audio is None:
                    logger.warning("⚠️ No audio track found in video")
                    return []
                audio.write_audiofile(temp_audio_path, logger=None, verbose=False)
            
            # Load audio and detect silence gaps
            audio_segment = AudioSegment.from_wav(temp_audio_path)
            silence_ranges = detect_silence(
                audio_segment,
                min_silence_len=1000,  # 1 second minimum silence
                silence_thresh=audio_tolerance  # Use configurable threshold
            )
            
            # Convert to seconds and return boundary points
            silence_boundaries = []
            for _, end_ms in silence_ranges:
                silence_boundaries.append(end_ms / 1000.0)  # End of silence = good cut point
            
            # Cleanup
            try:
                os.remove(temp_audio_path)
            except:
                pass
            
            logger.info(f"✅ Found {len(silence_boundaries)} silence boundaries")
            return silence_boundaries
            
        except Exception as e:
            logger.error(f"❌ Silence detection failed: {e}")
            raise

    def frames_to_timecode(self, frame_number: int, fps: float) -> str:
        """
        Convert frame number to timecode - exact from original repo
        """
        total_seconds = frame_number / fps
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        frames = int((total_seconds % 1) * fps)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{frames:02d}"

    def seconds_to_timestamp(self, seconds: float) -> str:
        """Convert seconds to timestamp format (HH:MM:SS.FF)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        centiseconds = int((seconds % 1) * 100)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"

    def scene_cut_single(self, video_path: str, options: dict = None) -> dict:
        """
        Main video analysis function - exact from original repo scene_cut.py
        Combines scene detection with audio analysis for optimal cuts
        """
        if options is None:
            options = {}
        
        # Extract options (original repo parameters)
        min_segment_length = options.get("min_segment_length", 2.0)
        max_segments = options.get("max_segments", 10)
        scene_threshold = options.get("scene_threshold", 30.0)
        audio_tolerance = options.get("audio_tolerance", -40.0)
        use_rgb_detect = options.get("use_rgb_detect", False)
        
        # Use audio analysis if audio_tolerance is provided (not None)
        use_audio_analysis = audio_tolerance is not None
        
        try:
            logger.info(f"🎬 Starting video analysis: {video_path}")
            
            # Get video duration
            video_duration = self.get_video_duration(video_path)
            
            # Step 1: Scene detection (original repo primary method)
            if use_rgb_detect:
                # Use RGB-based detection (scene_rgb_detect.py)
                rgb_scenes = self.scene_rgb_detect(video_path)
                # Convert frame-based scenes to time-based
                scene_list = []
                cap = cv2.VideoCapture(video_path)
                fps = cap.get(cv2.CAP_PROP_FPS) or 24
                cap.release()
                
                from scenedetect.frame_timecode import FrameTimecode
                for start_frame, end_frame in rgb_scenes:
                    start_time = FrameTimecode(start_frame / fps, fps=fps)
                    end_time = FrameTimecode(end_frame / fps, fps=fps)
                    scene_list.append((start_time, end_time))
            else:
                # Use ContentDetector (main method in scene_cut.py)
                scene_list = self.get_scene_list(video_path, scene_threshold)
            
            if not scene_list:
                raise Exception("Scene detection failed and no scenes were found")
            
            # Step 2: Get silence boundaries for cut optimization
            silence_points = []
            if use_audio_analysis and self.audio_processing_available:
                logger.info("🎵 Finding silence boundaries for cut optimization...")
                silence_points = self.find_silence_boundaries_simple(video_path, audio_tolerance)
            
            # Step 3: Convert scenes to cut points and optimize with silence
            cut_points = []
            for start_scene, end_scene in scene_list:
                duration = end_scene.get_seconds() - start_scene.get_seconds()
                if duration >= min_segment_length:
                    end_time = end_scene.get_seconds()
                    # Try to align with nearby silence points
                    best_cut = end_time
                    for silence_point in silence_points:
                        if abs(silence_point - end_time) < 2.0:  # Within 2 seconds
                            best_cut = silence_point
                            break
                    cut_points.append(best_cut)
            
            # Remove duplicates and sort
            cut_points = sorted(list(set(cut_points)))
            
            # Step 4: Limit number of cut points
            if len(cut_points) > max_segments - 1:
                logger.info(f"📊 Limiting cut points from {len(cut_points)} to {max_segments - 1}")
                cut_points = cut_points[:max_segments - 1]
            
            # Step 5: Create segments from cut points
            segments = []
            start_time = 0.0
            
            for i, cut_point in enumerate(cut_points):
                if cut_point > start_time + min_segment_length:
                    segment = {
                        "start": self.seconds_to_timestamp(start_time),
                        "end": self.seconds_to_timestamp(cut_point),
                        "start_seconds": start_time,
                        "end_seconds": cut_point,
                        "duration": cut_point - start_time,
                        "segment_id": i + 1
                    }
                    segments.append(segment)
                    logger.info(f"📽️ Segment {i+1}: {segment['start']} - {segment['end']} ({segment['duration']:.1f}s)")
                    start_time = cut_point
            
            # Add final segment if remaining duration is sufficient
            if video_duration - start_time >= min_segment_length:
                segment = {
                    "start": self.seconds_to_timestamp(start_time),
                    "end": self.seconds_to_timestamp(video_duration),
                    "start_seconds": start_time,
                    "end_seconds": video_duration,
                    "duration": video_duration - start_time,
                    "segment_id": len(segments) + 1
                }
                segments.append(segment)
                logger.info(f"📽️ Final segment: {segment['start']} - {segment['end']} ({segment['duration']:.1f}s)")
            
            # Determine analysis method used
            if use_rgb_detect:
                analysis_method = "rgb_detection"
            else:
                analysis_method = "content_detection"
            
            if use_audio_analysis and self.audio_processing_available:
                analysis_method += "+audio_silence"
            
            # Create result (matching original repo output structure)
            result = {
                "segments": segments,
                "segment_count": len(segments),
                "total_duration": video_duration,
                "analysis_method": analysis_method,
                "video_path": video_path,
                "scene_threshold": scene_threshold,
                "original_scenes_detected": len(scene_list),
                "cut_points_generated": len(cut_points),
                "options_used": options,
                "capabilities": {
                    "scene_detection": self.scene_detect_available,
                    "audio_processing": self.audio_processing_available
                }
            }
            
            logger.info(f"✅ Video analysis completed: {len(segments)} final segments")
            return result
            
        except Exception as e:
            logger.error(f"❌ Video analysis failed: {e}")
            raise

    def get_formatted_timestamps(self, segments: List[Dict]) -> str:
        """Convert segments to simple timestamp format"""
        if not segments:
            return ""
            
        formatted_parts = []
        for segment in segments:
            start = segment["start"][:8]  # HH:MM:SS
            end = segment["end"][:8]      # HH:MM:SS
            formatted_parts.append(f"{start}-{end}")
        
        return ", ".join(formatted_parts)







# Convenience functions for direct usage
def analyze_video_file(video_path: str, **kwargs) -> Dict:
    """
    Convenience function to analyze a video file
    
    Args:
        video_path: Path to video file
        **kwargs: Analysis options
        
    Returns:
        Analysis results dictionary
    """
    analyzer = VideoAutoCutOriginal()
    return analyzer.scene_cut_single(video_path, kwargs)

def get_video_segments(video_path: str, simple_format: bool = False, **kwargs) -> Dict:
    """
    Get video segments in specified format
    
    Args:
        video_path: Path to video file
        simple_format: If True, return simple timestamp format
        **kwargs: Analysis options
        
    Returns:
        Formatted results dictionary
    """
    analyzer = VideoAutoCutOriginal()
    result = analyzer.scene_cut_single(video_path, kwargs)
    
    if simple_format:
        return {
            "timestamps": analyzer.get_formatted_timestamps(result["segments"]),
            "segment_count": result["segment_count"],
            "video_duration": result["total_duration"]
        }
    else:
        # Add formatted timestamps to detailed result
        result["formatted_timestamps"] = analyzer.get_formatted_timestamps(result["segments"])
        return result

if __name__ == "__main__":
    # Example usage
    print("🎬 Video Auto Cut - Standalone Test")
    print("=" * 40)
    
    # Test video path (update with your video)
    test_video = "videos/example.mp4"
    
    if os.path.exists(test_video):
        try:
            # Simple analysis
            result = get_video_segments(test_video, simple_format=True)
            print(f"📹 Simple format: {result['timestamps']}")
            
            # Detailed analysis
            detailed = get_video_segments(test_video, use_audio_analysis=True)
            print(f"🔍 Found {detailed['segment_count']} segments")
            print(f"📊 Method: {detailed['analysis_method']}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    else:
        print(f"⚠️  Test video not found: {test_video}")
        print("Update the test_video path to test the functionality.")