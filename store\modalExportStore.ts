import { create } from 'zustand';

interface ExportModalState {
  modalExport: boolean;
  setModalExport: (val: boolean) => void;
  openExportModal: () => void;
  closeExportModal: () => void;
}

export const useExportModal = create<ExportModalState>((set) => ({
  modalExport: false,
  setModalExport: (val) => set({ modalExport: val }),
  openExportModal: () => set({ modalExport: true }),
  closeExportModal: () => set({ modalExport: false }),
}));

