import { useState, useCallback, useEffect } from 'react';
import { Overlay, OverlayType, SoundOverlay, ClipOverlay } from '../types';
import { getEffectiveFPS } from '../utils/fps-utils';

// Types
interface DownloadMessage {
  type: "success" | "error" | null;
  message: string;
}

interface UseVideoDownloadProps {
  overlays: Overlay[];
  selectedSegment: any | null;
  videoType: string; // 'mp4'
  durationInFrames?: number;
  compositionWidth?: number;
  compositionHeight?: number;
  projectUrl?: string;
  pathRef?: React.MutableRefObject<string | null>;
}

interface UseVideoDownloadReturn {
  isDownloadingVideo: boolean;
  downloadMessage: DownloadMessage;
  setDownloadMessage: React.Dispatch<React.SetStateAction<DownloadMessage>>;
  downloadVideoWithAudio: () => Promise<void>;
}

// Safe Electron API access
const safeElectronAPI = {
  showSaveDialog: async (options: any): Promise<any> => {
    if (typeof window !== "undefined" && (window as any).electronAPI?.showSaveDialog) {
      return await (window as any).electronAPI.showSaveDialog(options);
    }
    return { canceled: true };
  },
  combineVideoAudio: async (videoPath: string, audioSegments: any[], outputPath: string, useVideoAudio: boolean = false, videoTrimming: any = null): Promise<any> => {
    if (typeof window !== "undefined" && (window as any).electronAPI?.combineVideoAudio) {
      return await (window as any).electronAPI.combineVideoAudio(videoPath, audioSegments, outputPath, useVideoAudio, videoTrimming);
    }
    throw new Error("Video combination API not available");
  }
};

export const useVideoDownload = ({
  overlays,
  selectedSegment,
  videoType,
  durationInFrames = 150,
  compositionWidth = 1280,
  compositionHeight = 720,
  projectUrl,
  pathRef
}: UseVideoDownloadProps): UseVideoDownloadReturn => {
  const [isDownloadingVideo, setIsDownloadingVideo] = useState<boolean>(false);
  const [lastDownloadTime, setLastDownloadTime] = useState<number>(0);
  const [downloadMessage, setDownloadMessage] = useState<DownloadMessage>({ 
    type: null, 
    message: "" 
  });

  // Helper to convert blob URL to temporary file path
  const convertBlobToTempFile = async (blobUrl: string, extension: string): Promise<string> => {
    try {
      console.log(`📁 Converting blob to temp file: ${blobUrl}`);
      
      // Fetch the blob data
      const response = await fetch(blobUrl);
      const arrayBuffer = await response.arrayBuffer();
      
      // Create a temporary file path
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(7);
      const tempFileName = `temp_${timestamp}_${random}.${extension}`;
      
      // Use Electron API to write the temporary file
      if (typeof window !== "undefined" && (window as any).electronAPI?.writeFileBuffer) {
        // Create temp file path manually (avoiding path module issues)
        const isWindows = navigator.userAgent.toLowerCase().includes('win');
        const tempPath = isWindows ? 
          `C:\\temp\\${tempFileName}` : 
          `/tmp/${tempFileName}`;
        
        console.log(`💾 Writing temp file: ${tempPath}`);
        const writeResult = await (window as any).electronAPI.writeFileBuffer(tempPath, arrayBuffer);
        
        if (writeResult && writeResult.success) {
          console.log(`✅ Temp file created: ${tempPath}`);
          return tempPath;
        } else {
          throw new Error('Failed to write temporary file');
        }
      } else {
        throw new Error('Electron writeFileBuffer API not available');
      }
      
    } catch (error) {
      console.error('Error converting blob to temp file:', error);
      throw new Error(`Failed to convert blob to temporary file: ${(error as Error).message}`);
    }
  };

  // Helper for processing video with audio
  const processVideoWithAudio = async (): Promise<void> => {
    try {
      const timestamp = Date.now();
      
      // Step 1: Get video overlays based on selection
      const { videoOverlays, audioOverlays } = getOverlaysForDownload();

      if (videoOverlays.length === 0) {
        throw new Error("No video found for download");
      }

      const primaryVideo = videoOverlays[0]; // Use selected video or first video based on selection
      console.log(`🎬 Using primary video: ${primaryVideo.src}`);
      
      // Step 2: Prepare video trimming info first (before processing audio)
      let videoTrimming = null;
      const isVideoSegmentSelected = selectedSegment && 
        overlays.some(overlay => 
          overlay.id === selectedSegment.id && 
          overlay.type === OverlayType.VIDEO && 
          overlay.row === 0
        );

      if (isVideoSegmentSelected) {
        // Use only start time - calculate duration from start to end
        const startTime = selectedSegment.start || 0;
        const endTime = selectedSegment.end || null;
        const duration = endTime ? (endTime - startTime) : null;
        
        videoTrimming = {
          startTime,
          duration
        };
        
        console.log(`✂️ Video trimming: start=${startTime}s, duration=${duration}s`);
      }
      
      // Step 3: Process audio overlays (now with video trimming info available)
      let audioSegments = [];
      let useVideoAudio = false;
      
      if (audioOverlays.length > 0) {
        console.log(`🎵 Found ${audioOverlays.length} audio segments in second row`);
        const currentFPS = getEffectiveFPS(overlays) || 30;
        
        // Process all audio overlays
        for (let i = 0; i < audioOverlays.length; i++) {
          const audioOverlay = audioOverlays[i];
          let startTime = audioOverlay.from / currentFPS;
          const duration = audioOverlay.durationInFrames / currentFPS;
          
          // Adjust audio timing if video is being trimmed
          if (videoTrimming && videoTrimming.startTime) {
            startTime = Math.max(0, startTime - videoTrimming.startTime);
            console.log(`🎵 Audio segment ${i + 1}: adjusted to start at ${startTime}s (offset: ${videoTrimming.startTime}s), duration ${duration}s`);
          } else {
            console.log(`🎵 Audio segment ${i + 1}: starts at ${startTime}s, duration ${duration}s`);
          }
          
          // Convert audio blob to temporary file
          const audioFilePath = await convertBlobToTempFile(audioOverlay.src, `${i}.wav`);
          
          audioSegments.push({
            filePath: audioFilePath,
            startTime: startTime,
            duration: duration,
            src: audioOverlay.src
          });
        }
      } else {
        console.log(`🔇 No audio found in second row - will use original video audio`);
        useVideoAudio = true;
      }
      
      // Step 4: Convert video blob to temporary file
      console.log(`🎬 Converting video blob to temporary file...`);
      const videoFilePath = await convertBlobToTempFile(primaryVideo.src, 'mp4');
      
      // Step 5: Get output file path from user
      const result = await safeElectronAPI.showSaveDialog({
        defaultPath: `video_with_audio_${timestamp}.mp4`,
        filters: [
          { name: 'MP4 Video', extensions: ['mp4'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (result.canceled || !result.filePath) {
        console.log('User canceled save dialog');
        return;
      }

      console.log(`💾 Output path: ${result.filePath}`);

      // Step 6: Combine video and audio using Electron API
      if (useVideoAudio) {
        console.log(`🔄 Using original video audio (no second row audio found)...`);
      } else {
        console.log(`🔄 Combining video with ${audioSegments.length} audio segments...`);
      }
      console.log(`🔄 Calling combineVideoAudio: ${audioSegments.length} audio segments, trimming: ${videoTrimming ? 'yes' : 'no'}`);
      
      const combineResult = await safeElectronAPI.combineVideoAudio(
        videoFilePath,
        audioSegments, // Pass all audio segments with timing
        result.filePath,
        useVideoAudio, // Pass flag to use video audio
        videoTrimming // Pass video trimming parameters
      );

      if (combineResult.success) {
        console.log(`✅ Video with audio saved: ${result.filePath}`);
        setDownloadMessage({
          type: "success",
          message: `MP4 video downloaded successfully to: ${result.filePath}`
        });
        
        // Clean up temporary files
        try {
          if (typeof window !== "undefined" && (window as any).electronAPI?.deleteFile) {
            await (window as any).electronAPI.deleteFile(videoFilePath);
            // Clean up all audio temporary files
            for (const audioSegment of audioSegments) {
              await (window as any).electronAPI.deleteFile(audioSegment.filePath);
            }
            console.log(`🗑️ Temporary files cleaned up`);
          }
        } catch (cleanupError) {
          console.warn('⚠️ Failed to clean up temporary files:', cleanupError);
        }
      } else {
        throw new Error(combineResult.error || 'Video combination failed');
      }
      
    } catch (error) {
      console.error('Error in video processing:', error);
      throw error;
    }
  };

  // Strategy to determine what overlays to include
  const getOverlaysForDownload = (): { videoOverlays: ClipOverlay[], audioOverlays: SoundOverlay[] } => {
    // Check if a specific video segment is selected
    const isVideoSegmentSelected = selectedSegment && 
      overlays.some(overlay => 
        overlay.id === selectedSegment.id && 
        overlay.type === OverlayType.VIDEO && 
        overlay.row === 0
      );

    let videoOverlays: ClipOverlay[];
    let audioOverlays: SoundOverlay[];

    if (isVideoSegmentSelected) {
      // Download specific video segment + all audio from second row
      const selectedClipOverlay = overlays.find(overlay => 
        overlay.id === selectedSegment.id && 
        overlay.type === OverlayType.VIDEO && 
        overlay.row === 0
      ) as ClipOverlay;
      
      videoOverlays = selectedClipOverlay ? [selectedClipOverlay] : [];
      audioOverlays = overlays.filter(
        overlay => overlay.type === OverlayType.SOUND && overlay.row === 1
      ) as SoundOverlay[];
    } else {
      // Download entire timeline - all video from first row + all audio from second row
      videoOverlays = overlays.filter(overlay => 
        overlay.type === OverlayType.VIDEO && overlay.row === 0
      ) as ClipOverlay[];
      
      audioOverlays = overlays.filter(overlay => 
        overlay.type === OverlayType.SOUND && overlay.row === 1
      ) as SoundOverlay[];
    }

    return { videoOverlays, audioOverlays };
  };

  // Main download function
  const downloadVideoWithAudio = useCallback(async (): Promise<void> => {
    const now = Date.now();
    
    // Debounce mechanism
    if (isDownloadingVideo || now - lastDownloadTime < 2000) {
      console.log("🚫 Video download ignored - too frequent or already in progress");
      return;
    }
    
    const processId = Math.random().toString(36).substring(7);
    console.log(`🚀 Starting video download process (ID: ${processId})`);
    
    setIsDownloadingVideo(true);
    setLastDownloadTime(now);
    
    // Clear any previous download messages
    setDownloadMessage({ type: null, message: "" });
    
    try {
      const { videoOverlays, audioOverlays } = getOverlaysForDownload();

      if (videoOverlays.length === 0) {
        setDownloadMessage({
          type: "error",
          message: "❌ No video found for download"
        });
        return;
      }

      const strategy = !selectedSegment || !overlays.some(o => 
        o.id === selectedSegment.id && o.type === OverlayType.VIDEO && o.row === 0
      ) ? 'Entire timeline' : 'Selected video segment';

      console.log(`📥 Processing ${videoOverlays.length} video segments and ${audioOverlays.length} audio segments`);
      console.log(`🎯 Strategy: ${strategy}`);

      await processVideoWithAudio();
      
      console.log(`🏁 Video process completed (ID: ${processId})`);
      
    } catch (error) {
      console.error("Error in downloadVideoWithAudio:", error);
      setDownloadMessage({
        type: "error",
        message: `❌ Error creating video: ${(error as Error).message}`
      });
    } finally {
      setIsDownloadingVideo(false);
    }
  }, [
    isDownloadingVideo, 
    lastDownloadTime, 
    overlays, 
    selectedSegment, 
    videoType, 
    durationInFrames
  ]);

  return {
    isDownloadingVideo,
    downloadMessage,
    setDownloadMessage,
    downloadVideoWithAudio
  };
};