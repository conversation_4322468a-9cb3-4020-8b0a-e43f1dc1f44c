import React, { useEffect } from "react";

interface ItemSettingProps {
  children: React.ReactNode;
  isActive?: boolean;
  isDisabled?: boolean;
}

const ItemSetting: React.FC<ItemSettingProps> = ({
  children,
  isActive = false,
  isDisabled = false,
}) => {
  return (
    <div
      className={`lg:p-1 xl:p-3 relative rounded-[68px] flex justify-center items-center overflow-hidden ${
        isActive ? "bg-zinc-300/25" : "bg-transparent"
      }  transition-colors duration-200 ${
        !isDisabled ? "hover:bg-zinc-300/10 cursor-pointer" : "cursor-default"
      } `}
    >
      {children}
    </div>
  );
};

export default ItemSetting;
