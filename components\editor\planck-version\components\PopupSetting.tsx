import React from "react";

interface PopupSettingProps {
  children: React.ReactNode;
}

const PopupSetting: React.FC<PopupSettingProps> = ({ children }) => {
  return (
    <div className=" p-2 bg-[#39393966] rounded-[22px] box-popup ">
      <div className="overflow-y-auto custom-scroll lg:w-[200px] xl:w-[316px] lg:max-h-[60vh]">
        {children}
      </div>
    </div>
  );
};

export default PopupSetting;
