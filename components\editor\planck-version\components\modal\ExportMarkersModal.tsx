import {
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material";
import { CheckedIcon, UncheckedIcon } from "../icon/CustomIcon";
import CustomModal from "../CustomModal";
import Button from "../Button";
import { useExportMarkersModal } from "@/store/modalExportMarkersStore";

const ExportMarkersModal = () => {
  const { modalExportMarkers, closeExportMarkersModal } = useExportMarkersModal();
  const handleExportMarkersModal = () => {
    closeExportMarkersModal();
  };
  return (
    <CustomModal
      open={modalExportMarkers}
      onClose={() => {
        handleExportMarkersModal();
      }}
      title="Export markers as.."
    >
      <FormControl>
        <RadioGroup
          className="gap-4"
          name="col-radio-buttons-group"
          defaultValue={"xml"}
        >
          <FormControlLabel
            value="xml"
            control={
              <Radio icon={<UncheckedIcon />} checkedIcon={<CheckedIcon />} />
            }
            label="XML"
            sx={{
              "& .MuiFormControlLabel-label": {
                fontSize: "14px",
                fontFamily: "Inter, sans-serif",
                color: "white",
              },
            }}
          />
          <FormControlLabel
            value="edl"
            control={
              <Radio icon={<UncheckedIcon />} checkedIcon={<CheckedIcon />} />
            }
            label="EDL"
            sx={{
              "& .MuiFormControlLabel-label": {
                fontSize: "14px",
                fontFamily: "Inter, sans-serif",
                color: "white",
              },
            }}
          />
          <FormControlLabel
            value="fcpxml"
            control={
              <Radio icon={<UncheckedIcon />} checkedIcon={<CheckedIcon />} />
            }
            label="FCPXML"
            sx={{
              "& .MuiFormControlLabel-label": {
                fontSize: "14px",
                fontFamily: "Inter, sans-serif",
                color: "white",
              },
            }}
          />
        </RadioGroup>
        <div className="flex gap-4 mt-8 items-center w-full">
          <Button
            customClass={"flex-grow shrink-0 text-white font-inter"}
            variant="danger"
            onClick={() => handleExportMarkersModal()}
          >
            Cancel
          </Button>
          <Button customClass={"flex-grow shrink-0 text-white font-inter"}>
            Import
          </Button>
        </div>
      </FormControl>
    </CustomModal>
  );
};

export default ExportMarkersModal;
