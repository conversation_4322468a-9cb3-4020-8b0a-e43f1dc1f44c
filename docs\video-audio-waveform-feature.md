# Video Audio Waveform Feature

## Overview

This feature adds audio waveform visualization below video thumbnails in the timeline. When a video overlay is displayed in the timeline, it now shows both the video keyframes (thumbnails) and a subtle audio waveform visualization at the bottom.

## Features

- **Automatic Audio Extraction**: Extracts audio data from video files using the Web Audio API
- **Waveform Visualization**: Displays a compact waveform below video thumbnails
- **Split Video Support**: Correctly handles split video overlays with different `videoStartTime` offsets
- **Responsive Design**: Adapts to the number of thumbnail slots available
- **Fallback Handling**: Provides graceful fallback when audio processing fails
- **Performance Optimized**: Uses caching and efficient rendering techniques

## Implementation Details

### New Components

#### 1. `useVideoAudioWaveform` Hook
- **Location**: `components/editor/[version]/hooks/use-video-audio-waveform.tsx`
- **Purpose**: Extracts audio waveform data from video files
- **Key Features**:
  - Fetches video file and decodes audio using Web Audio API
  - Handles `videoStartTime` offset for split videos
  - Provides configurable number of data points
  - Includes error handling with fallback data

```typescript
const { waveformData, isLoading } = useVideoAudioWaveform(
  videoSrc,
  videoStartTime,
  durationInFrames,
  { numPoints: 50, fps: 30 }
);
```

#### 2. `VideoAudioWaveform` Component
- **Location**: `components/editor/[version]/components/timeline/video-audio-waveform.tsx`
- **Purpose**: Renders the waveform visualization
- **Key Features**:
  - Compact design optimized for timeline display
  - Responsive to available space
  - Subtle visual styling that doesn't interfere with video thumbnails

### Modified Components

#### 1. `TimelineKeyframes` Component
- **Enhanced**: Added audio waveform visualization for video overlays
- **Changes**:
  - Imports and uses `useVideoAudioWaveform` hook
  - Renders `VideoAudioWaveform` component below video thumbnails
  - Includes waveform loading state in overall loading indicator

## Usage

The feature is automatically enabled for all video overlays. No additional configuration is required.

### For Video Overlays:
1. Video thumbnails are displayed as before
2. Audio waveform is automatically extracted and displayed below thumbnails
3. Waveform respects the `videoStartTime` for split videos
4. Loading states are handled gracefully

### Visual Design:
- Waveform appears as subtle blue bars below video thumbnails
- Height varies based on audio amplitude
- Compact 16px height to not interfere with video content
- Responsive to timeline zoom and slot count

## Technical Implementation

### Audio Processing Pipeline:
1. **Fetch**: Video file is fetched as ArrayBuffer
2. **Decode**: Web Audio API decodes audio data from video
3. **Analyze**: Audio samples are processed to generate amplitude peaks
4. **Normalize**: Peaks are normalized using 95th percentile to avoid outliers
5. **Cache**: Results are cached for performance

### Split Video Handling:
- Uses `videoStartTime` to calculate correct audio offset
- Ensures split videos show different waveform segments
- Maintains separate cache entries for different start times

### Error Handling:
- Graceful fallback when audio processing fails
- Provides subtle random waveform data as fallback
- Logs errors for debugging without breaking UI

## Performance Considerations

- **Caching**: Waveform data is cached to avoid reprocessing
- **Efficient Sampling**: Configurable number of data points (default: 50)
- **Lazy Loading**: Only processes audio when video overlay is visible
- **Memory Management**: Proper cleanup of audio contexts and resources

## Browser Compatibility

- Requires Web Audio API support (modern browsers)
- Falls back gracefully on unsupported browsers
- Tested with common video formats (MP4, WebM, etc.)

## Testing

Comprehensive test suite includes:
- Audio waveform extraction functionality
- Split video handling with different start times
- Error handling and fallback scenarios
- Performance and memory management
- Integration with existing keyframe system

## Future Enhancements

Potential improvements:
- Audio level indicators during playback
- Waveform scrubbing for precise audio navigation
- Visual indicators for audio peaks and silence
- Support for multiple audio tracks
- Real-time waveform updates during editing

## Troubleshooting

### Common Issues:

1. **No waveform displayed**: 
   - Check browser Web Audio API support
   - Verify video file has audio track
   - Check console for error messages

2. **Incorrect waveform for split videos**:
   - Ensure `videoStartTime` is properly set
   - Verify cache is not corrupted

3. **Performance issues**:
   - Reduce `numPoints` configuration
   - Check for memory leaks in audio contexts

### Debug Information:
- Error messages are logged to console with prefix "Error processing video audio waveform:"
- Fallback waveform data is used when processing fails
- Loading states are properly managed and displayed
