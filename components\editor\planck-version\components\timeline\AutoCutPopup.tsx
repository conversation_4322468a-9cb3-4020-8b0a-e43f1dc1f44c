import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Zap } from "lucide-react";
import { useSegmentStore } from "@/store/segmentStore";
import { useCutStore } from "@/store/cutStore";
import { useEditorContext } from "../../contexts/editor-context";
import {
  getLastSavedVideoBuffer,
  saveVideoProject,
} from "../../utils/save-project";
import { useAppContext } from "../../hooks/useAppContext";

interface AutoCutPopupProps {
  onCreateSegment: (timeInSeconds: number) => void;
  currentFrame: number;
  fps: number;
  formatTime: (frames: number) => string;
}

interface AutoCutConfig {
  min_segment_length: number;
  max_segments: number;
  scene_threshold: number;
  audio_tolerance: number;
  use_rgb_detect: boolean;
}

export const AutoCutPopup: React.FC<AutoCutPopupProps> = ({
  onCreateSegment,
  fps,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [config, setConfig] = useState<AutoCutConfig>({
    min_segment_length: 100,
    max_segments: 50,
    scene_threshold: 20,
    audio_tolerance: -40,
    use_rgb_detect: false,
  });
  const { isLoading: isGenerating } = useAppContext();
  const setTimeFrameSegment = useSegmentStore(
    (state) => state.setTimeFrameSegment
  );
  const addCutTime = useCutStore((state) => state.addCutTime);

  const handleConfigChange = (
    key: keyof AutoCutConfig,
    value: boolean | number
  ) => {
    setConfig((prev) => ({
      ...prev,
      [key]: value,
    }));
  };
  // Updated to match CreateSegmentPopup's parseTimeInput format (HH:MM:SS.FF)
  const parseTimeInput = (
    input: string
  ): { timeInSeconds: number; dataTimeSegment: any } | null => {
    // First try the full format with frames (HH:MM:SS.FF)
    const timeRegex = /^(\d{1,2}):(\d{1,2}):(\d{1,2})\.(\d{1,3})$/;
    let match = input.match(timeRegex);

    if (match) {
      const [, hours, minutes, seconds, frames] = match;

      // Validate frame number against actual FPS
      const frameNumber = parseInt(frames);
      if (frameNumber >= fps) {
        return null; // Invalid frame number for this FPS
      }

      const totalFrames =
        parseInt(hours) * 3600 * fps +
        parseInt(minutes) * 60 * fps +
        parseInt(seconds) * fps +
        frameNumber;

      const timeInSeconds = totalFrames / fps;

      const dataTimeSegment = {
        input,
        hours: parseInt(hours),
        minutes: parseInt(minutes),
        seconds: parseInt(seconds),
        frames: frameNumber,
        fps: fps,
      };

      return { timeInSeconds, dataTimeSegment };
    }

    // Fallback to try without frames (HH:MM:SS)
    const fallbackRegex = /^(\d{1,2}):(\d{1,2}):(\d{1,2})$/;
    match = input.match(fallbackRegex);

    if (match) {
      const [, hours, minutes, seconds] = match;
      const totalSeconds =
        parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds);

      const dataTimeSegment = {
        input: `${input}.00`,
        hours: parseInt(hours),
        minutes: parseInt(minutes),
        seconds: Math.floor(parseFloat(seconds)),
        frames: 0,
        fps: fps,
      };

      return { timeInSeconds: totalSeconds, dataTimeSegment };
    }

    return null;
  };
  const handleProcess = async () => {
    setIsProcessing(true);
    setErrorMessage("");
    try {
      // Check if Electron API is available
      if (!window.electronAPI?.videoAutoCut) {
        setErrorMessage(
          "This feature is only available in the desktop application."
        );
        setIsProcessing(false);
        return;
      }

      // Get current project metadata (if exists)
      let currentMetadata = null;
      try {
        currentMetadata = await window.electronAPI.getLastSavedMetadata();
      } catch (error) {
        console.log("No existing saved project found");
      }

      // Step 1: Save the video with minimal metadata (only to get saveDir)
      // Get video buffer
      const videoBuffer = await getLastSavedVideoBuffer();
      if (!videoBuffer) {
        throw new Error(
          "No video buffer available. Please ensure a video is loaded."
        );
      }

      // Save with minimal metadata - this will only update saveDir
      const saveResult = await window.electronAPI.onSaveVideo(
        videoBuffer,
        {
          // Preserve existing metadata if available
          ...(currentMetadata || {}),

          // Minimal required updates
          source: "auto-cut-minimal",
          autoSave: true,

          // Explicitly preserve critical existing data
          ...(currentMetadata?.timeFrameSegment && {
            timeFrameSegment: currentMetadata.timeFrameSegment,
          }),
          ...(currentMetadata?.segments && {
            segments: currentMetadata.segments,
          }),
        }
        // Remove third argument (AI analytics should be inside metadata.segments if needed)
      );

      if (!saveResult.success) {
        throw new Error(
          `Failed to save project: ${saveResult.message || "Unknown error"}`
        );
      }

      // Get the updated saveDir from the saved project
      const updatedMetadata = await window.electronAPI.getLastSavedMetadata();
      if (!updatedMetadata?.saveDir) {
        throw new Error("Could not retrieve save directory");
      }

      // Prepare config with video path
      const videoPath = `${updatedMetadata.saveDir}/${updatedMetadata.video}`;
      const configWithVideoPath = {
        ...config,
        video_path: videoPath,
      };

      // Call the video-auto-cut API via Electron
      const result = await window.electronAPI.videoAutoCut(configWithVideoPath);
      if (!result?.success) {
        setErrorMessage(result?.err_msg);
        return;
      }

      if (result.data?.timesplit) {
        const timesplits = result.data.timesplit;
        const allDataTimeSegments: any[] = [];
        const validTimes: number[] = [];

        // Process each timesplit into segments
        timesplits.forEach((timeStr: any) => {
          const formattedTime = timeStr.includes(".")
            ? timeStr
            : `${timeStr}.00`;
          const parsed = parseTimeInput(formattedTime);

          if (!parsed) {
            console.warn(`Skipping invalid time format: ${timeStr}`);
            return;
          }

          // Store frame segment data (matches CreateSegmentPopup format)
          allDataTimeSegments.push({
            input: formattedTime,
            hours: parsed.dataTimeSegment.hours,
            minutes: parsed.dataTimeSegment.minutes,
            seconds: parsed.dataTimeSegment.seconds,
            frames: parsed.dataTimeSegment.frames,
            fps: fps,
          });

          validTimes.push(parsed.timeInSeconds);
        });

        // Sort times in descending order (matches CreateSegment behavior)
        const sortedTimes = [...validTimes].sort((a, b) => b - a);

        // Save to segment store (matches CreateSegmentPopup)
        setTimeFrameSegment(allDataTimeSegments);

        // Store cut points in cutStore without overwriting existing data
        if (allDataTimeSegments && allDataTimeSegments.length > 0) {
          allDataTimeSegments.forEach((cutTimeData) => {
            addCutTime(cutTimeData);
          });
        }

        // First, let the auto-cut segments be saved with minimal metadata
        // This allows the backend to handle the segment generation from timeFrameSegment
        await saveVideoProject();

        // Get the updated cut times from store
        const updatedCutTimes = useCutStore.getState().cutTimes;

        // Save just the cut times - let backend generate segments automatically
        const initialSaveResult = await window.electronAPI.onSaveVideo(
          await getLastSavedVideoBuffer(),
          {
            timeFrameSegment: updatedCutTimes,
            source: "auto-cut-initial",
          }
        );

        if (!initialSaveResult.success) {
          throw new Error(
            initialSaveResult.message || "Failed to save auto-cut segments"
          );
        }

        // Now do a second save to preserve any existing manual segments
        // Get the fresh metadata that includes the auto-generated segments
        const freshMetadata = await window.electronAPI.getLastSavedMetadata();

        // Merge existing segments with the newly generated auto-cut segments
        const finalSaveResult = await window.electronAPI.onSaveVideo(
          await getLastSavedVideoBuffer(),
          {
            ...freshMetadata,
            source: "auto-cut-final",
            // Let backend handle the complete merge
          }
        );

        if (!finalSaveResult.success) {
          throw new Error(
            finalSaveResult.message || "Failed to finalize auto-cut segments"
          );
        }

        // Create segments in UI
        sortedTimes.forEach((time) => {
          onCreateSegment(time);
        });

        setIsOpen(false);
      }
    } catch (error) {
      console.error("Auto cut failed:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          onClick={() => setErrorMessage("")}
          variant="outline"
          size="sm"
          className={`btn-auto-cut h-7 px-3 text-xs font-medium bg-red-500 hover:bg-red-700 text-white border-red-500 hover:border-red-700 ${
            isGenerating && "pointer-events-none opacity-50"
          }`}
        >
          <Zap className="h-3 w-3 mr-1" />
          Auto Cut
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Auto Cut Configuration</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-3">
            {errorMessage && (
              <div className="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400">
                {errorMessage}
              </div>
            )}

            <div className="space-y-2">
              <Label
                htmlFor="min_segment_length"
                className="text-sm font-medium"
              >
                Min Segment Length (miliseconds)
              </Label>
              <Input
                id="min_segment_length"
                type="number"
                value={config.min_segment_length}
                onChange={(e) =>
                  handleConfigChange(
                    "min_segment_length",
                    parseFloat(e.target.value)
                  )
                }
                className="text-sm"
                step="0.1"
                min="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="max_segments" className="text-sm font-medium">
                Max Segments
              </Label>
              <Input
                id="max_segments"
                type="number"
                value={config.max_segments}
                onChange={(e) =>
                  handleConfigChange("max_segments", parseInt(e.target.value))
                }
                className="text-sm"
                min="1"
                max="20"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="scene_threshold" className="text-sm font-medium">
                Scene Threshold (0-100)
              </Label>
              <Input
                id="scene_threshold"
                type="number"
                value={config.scene_threshold}
                onChange={(e) =>
                  handleConfigChange(
                    "scene_threshold",
                    parseFloat(e.target.value)
                  )
                }
                className="text-sm"
                step="1"
                min="0"
                max="100"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="audio_tolerance" className="text-sm font-medium">
                Audio Tolerance (dB)
              </Label>
              <Input
                id="audio_tolerance"
                type="number"
                value={config.audio_tolerance}
                onChange={(e) =>
                  handleConfigChange(
                    "audio_tolerance",
                    parseFloat(e.target.value)
                  )
                }
                className="text-sm"
                step="0.1"
                min="0"
              />
            </div>

            {/* <div className="flex items-center space-x-2">
              <Checkbox
                id="use_rgb_detect"
                checked={config.use_rgb_detect}
                onCheckedChange={(checked) =>
                  handleConfigChange("use_rgb_detect", checked as boolean)
                }
              />
              <Label htmlFor="use_rgb_detect" className="text-sm font-medium">
                Use RGB Detection
              </Label>
            </div> */}
          </div>

          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1 btn-close-popup"
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleProcess}
              className="flex-1 bg-blue-400 hover:bg-blue-500 text-white"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Processing...
                </>
              ) : (
                "Process"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
