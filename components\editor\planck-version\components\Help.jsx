import { Link } from "react-router-dom";

const Help = () => {
  return (
    <div className="container mx-auto text-white">
      <div className="max-h-[93vh] overflow-y-auto p-10">
        <h1 className="text-2xl font-bold mb-4">Help</h1>
        <h3 className="text-lg font-semibold mt-4">Introduction</h3>
        <p>
          Welcome to our platform! Here, you can upload a video, let our AI
          process it, and receive valuable insights along with a processed video
          and sound output.
        </p>
        <h3 className="text-lg font-semibold mt-4">How It Works</h3>
        <ol className="list-decimal ml-6">
          <li>
            <strong>Upload a Video</strong>
            <ul className="list-disc ml-6">
              <li>
                Click on the upload button and select a video file from your
                device.
              </li>
            </ul>
          </li>
          <li>
            <strong>AI Processing</strong>
            <ul className="list-disc ml-6">
              <li>
                Our AI will analyze your video and generate a response that
                includes:
              </li>
              <li>A processed video with enhanced sound.</li>
              <li>
                Information on detected sounds such as environment, weather,
                character size, and footwear.
              </li>
            </ul>
          </li>
          <li>
            <strong>Review the Results</strong>
            <ul className="list-disc ml-6">
              <li>Once processing is complete, you will receive:</li>
              <li>The updated video with sound.</li>
              <li>A breakdown of the identified sounds.</li>
            </ul>
          </li>
          <li>
            <strong>Download Options</strong>
            <ul className="list-disc ml-6">
              <li>You can download:</li>
              <li>The processed video.</li>
              <li>The extracted sound separately.</li>
            </ul>
          </li>
        </ol>
        <h3 className="text-lg font-semibold mt-4">
          Frequently Asked Questions (FAQ)
        </h3>
        <ul className="list-disc ml-6">
          <li>
            <strong>What file formats are supported?</strong>
            <p>We support MP4 video format.</p>
          </li>
          <li>
            <strong>How long does AI processing take?</strong>
            <p>
              Processing time depends on the length and complexity of the video
              but typically takes a few minutes.
            </p>
          </li>
          <li>
            <strong>Can I download just the sound?</strong>
            <p>
              Yes, you can choose to download only the extracted sound if
              needed.
            </p>
          </li>
        </ul>
        <h3 className="text-lg font-semibold mt-4">Troubleshooting</h3>
        <ul className="list-disc ml-6">
          <li>
            If your upload fails, ensure your video is in a supported format and
            meets the size limit.
          </li>
          <li>Refresh the page if you experience any issues and try again.</li>
          <li>
            If processing takes too long, check your internet connection and try
            re-uploading the file.
          </li>
        </ul>
      </div>
    </div>
  );
};

export default Help;
